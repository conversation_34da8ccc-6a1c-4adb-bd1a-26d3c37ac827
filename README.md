# 工业视觉缺陷检测系统

工业视觉缺陷检测系统是一个基于计算机视觉技术的自动化质量检测系统，主要用于工业生产线上对产品进行缺陷检测。系统通过工业相机采集产品图像，利用深度学习算法进行缺陷识别和分类，并将检测结果反馈给PLC控制系统。

## 系统架构

系统采用模块化设计，主要包括以下模块：

1. **相机管理模块 (CameraManager)**：负责工业相机的管理和控制
2. **检测管理模块 (DetectorManager)**：负责检测任务的调度和管理
3. **PLC通信模块 (PlcHandler)**：负责与PLC设备的通信
4. **配置管理模块 (ConfigManager)**：负责系统配置的管理
5. **数据管理模块 (DataManager)**：负责检测数据的存储、查询和导出
6. **日志模块 (Logger)**：负责系统日志的记录和管理

## 项目结构

```
.
├── architecture_design.md      # 系统架构设计文档
├── config/                     # 配置管理模块
├── camera/                     # 相机管理模块
├── detector/                   # 检测管理模块
├── plc/                        # PLC通信模块
├── data/                       # 数据管理模块
├── ui/                         # 用户界面模块
│   ├── main_window.py          # 主窗口
│   ├── home_page.py            # 首页（系统总览）
│   ├── camera_page.py          # 相机预览页面
│   ├── detection_page.py       # 检测结果页面
│   ├── config_page.py          # 配置页面
│   ├── log_page.py             # 日志页面
│   ├── resources.py            # 资源文件
│   └── utils.py                # 工具函数
├── main.py                     # 系统主入口
├── run_ui.py                   # UI界面入口
└── README.md                   # 项目说明文档
```

## 功能特性

- 多相机并行采集与检测
- 响应PLC触发信号
- 检测结果反馈给PLC
- 友好的用户界面（QFluentWidgets）
- 灵活的配置管理
- 高可靠性和实时性保证

## 安装和运行

### 环境要求

- Python 3.7+
- 相关依赖包（见pyproject.toml）

### 安装依赖

```bash
pip install -e .
```

### 运行系统

```bash
python main.py
```

### 运行UI界面

```bash
python run_ui.py
```

## 模块说明

### 相机管理模块 (camera)

负责工业相机的管理和控制，包括设备枚举、参数配置、图像采集等功能。

### 检测管理模块 (detector)

负责检测任务的调度和管理，支持多线程并发处理，提供统一的检测算法接口。

### PLC通信模块 (plc)

负责与PLC设备的通信，包括接收触发信号和发送检测结果。

### 配置管理模块 (config)

提供统一的配置管理接口，支持JSON和YAML格式的配置文件。

### 数据管理模块 (data)

负责检测数据的存储、查询和导出，使用SQLite数据库进行数据持久化。

### 日志模块 (logger)

负责系统日志的记录和管理。

## 开发指南

### 代码结构

系统采用面向对象的设计模式，各模块之间通过清晰的接口进行交互。

### 扩展开发

1. **添加新的检测算法**：继承`DetectorBase`类，实现相应的接口
2. **添加新的相机类型**：继承`CameraDevice`类，实现相应的接口
3. **添加新的PLC协议**：继承`PlcHandler`类，实现相应的接口

## 许可证

本项目采用MIT许可证，详见LICENSE文件。