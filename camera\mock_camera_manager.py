"""
模拟相机管理器，用于测试UI功能而不依赖实际的相机硬件
"""
import time
import numpy as np
from typing import List, Optional, Dict, Any


class MockCameraDevice:
    """模拟相机设备类"""

    def __init__(self, serial_number: str, model_name: str = "Mock Camera"):
        self.serial_number = serial_number
        self.model_name = model_name
        self.is_opened = False
        self.is_grabbing = False
        self.config_name = ""
        self.config_id = ""
        self.config_exposure_time = 10000
        self.config_gain = 10.0

    def open(self) -> int:
        """打开相机"""
        self.is_opened = True
        return 0  # MV_OK

    def close(self) -> int:
        """关闭相机"""
        self.is_opened = False
        self.is_grabbing = False
        return 0

    def start_grabbing(self) -> int:
        """开始取流"""
        if not self.is_opened:
            return -1
        self.is_grabbing = True
        return 0

    def stop_grabbing(self) -> int:
        """停止取流"""
        self.is_grabbing = False
        return 0

    def set_trigger_mode(self, is_trigger_mode: bool) -> int:
        """设置触发模式"""
        return 0

    def trigger_software(self) -> int:
        """软件触发一次"""
        return 0

    def get_frame(self, timeout: int = 1000) -> Optional[Dict[str, Any]]:
        """获取一帧图像"""
        if not self.is_opened or not self.is_grabbing:
            return None
        
        # 生成模拟图像数据
        width, height = 640, 480
        # 创建一个简单的彩色图像
        image = np.zeros((height, width, 3), dtype=np.uint8)
        
        # 根据序列号生成不同的颜色
        color_map = {
            "DA6382131": (255, 100, 100),  # 红色
            "DA6382302": (100, 255, 100),  # 绿色
            "DA6382254": (100, 100, 255),  # 蓝色
            "DA6382156": (255, 255, 100),  # 黄色
            "DA6382258": (255, 100, 255),  # 紫色
            "DA6292402": (100, 255, 255),  # 青色
        }
        
        color = color_map.get(self.serial_number, (128, 128, 128))
        image[:, :] = color
        
        # 添加一些文字标识
        import cv2
        text = f"{self.config_name} - {self.serial_number}"
        cv2.putText(image, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(image, f"Time: {time.strftime('%H:%M:%S')}", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        return {
            "width": width,
            "height": height,
            "frame_len": width * height * 3,
            "data": image.tobytes()
        }

    def set_float_value(self, param_name: str, value: float) -> int:
        """设置浮点型参数值"""
        return 0

    def set_bool_value(self, param_name: str, value: bool) -> int:
        """设置布尔型参数值"""
        return 0

    def set_enum_value(self, param_name: str, value: int) -> int:
        """设置枚举型参数值"""
        return 0


class MockCameraManager:
    """模拟相机管理器类"""

    def __init__(self, config_manager=None):
        self.devices: List[MockCameraDevice] = []
        self.device_map: Dict[str, MockCameraDevice] = {}
        self.configured_devices: List[MockCameraDevice] = []
        self.configured_device_map: Dict[str, MockCameraDevice] = {}
        self.config_manager = config_manager
        self.camera_configs = []
        self.is_initialized = True
        self._load_camera_configs()

    def _load_camera_configs(self):
        """从配置管理器加载相机配置"""
        if self.config_manager:
            self.camera_configs = self.config_manager.get("camera.devices", [])
        else:
            # 如果没有配置管理器，使用默认配置
            self.camera_configs = [
                {"id": "CAMERA_001", "name": "COD1", "serial_number": "DA6382131", "enabled": True, "exposure_time": 10000, "gain": 10.0},
                {"id": "CAMERA_002", "name": "COD2", "serial_number": "DA6382302", "enabled": True, "exposure_time": 10000, "gain": 10.0},
                {"id": "CAMERA_003", "name": "COD3", "serial_number": "DA6382254", "enabled": True, "exposure_time": 10000, "gain": 10.0},
                {"id": "CAMERA_004", "name": "COD4", "serial_number": "DA6382156", "enabled": True, "exposure_time": 10000, "gain": 10.0},
                {"id": "CAMERA_005", "name": "COD5", "serial_number": "DA6382258", "enabled": True, "exposure_time": 10000, "gain": 10.0},
                {"id": "CAMERA_006", "name": "COD6", "serial_number": "DA6292402", "enabled": True, "exposure_time": 10000, "gain": 10.0},
            ]

    def enum_devices(self, vendor_name: str = "") -> int:
        """枚举设备"""
        # 清空现有设备列表
        self.devices.clear()
        self.device_map.clear()
        self.configured_devices.clear()
        self.configured_device_map.clear()

        # 创建模拟设备
        for config in self.camera_configs:
            if config.get("enabled", True):
                serial = config.get("serial_number", "")
                if serial:
                    device = MockCameraDevice(serial, "Mock Camera")
                    self.devices.append(device)
                    self.device_map[serial] = device

        # 根据配置筛选设备
        self._filter_configured_devices()
        
        print(f"枚举到 {len(self.devices)} 个模拟相机设备")
        return 0

    def _filter_configured_devices(self):
        """根据配置筛选设备"""
        # 获取配置中的序列号列表
        configured_serials = {config.get("serial_number", "") for config in self.camera_configs if config.get("enabled", True)}
        
        # 筛选出配置中指定的设备
        for serial, device in self.device_map.items():
            if serial in configured_serials:
                self.configured_devices.append(device)
                self.configured_device_map[serial] = device
                
                # 为设备添加配置信息
                for config in self.camera_configs:
                    if config.get("serial_number") == serial:
                        device.config_name = config.get("name", f"相机_{serial}")
                        device.config_id = config.get("id", f"CAMERA_{len(self.configured_devices):03d}")
                        device.config_exposure_time = config.get("exposure_time", 10000)
                        device.config_gain = config.get("gain", 10.0)
                        break

    def get_device_count(self) -> int:
        """获取设备数量"""
        return len(self.devices)

    def get_configured_device_count(self) -> int:
        """获取配置中指定的设备数量"""
        return len(self.configured_devices)

    def get_configured_devices(self) -> List[MockCameraDevice]:
        """获取配置中指定的设备列表"""
        return self.configured_devices.copy()

    def get_configured_device_by_serial(self, serial_number: str) -> Optional[MockCameraDevice]:
        """通过序列号获取配置中的设备"""
        return self.configured_device_map.get(serial_number)

    def open_device(self, device_identifier) -> int:
        """打开设备"""
        device = None
        if isinstance(device_identifier, str):
            device = self.get_configured_device_by_serial(device_identifier)
        
        if device is None:
            return -1
        
        return device.open()

    def close_device(self, device_identifier) -> int:
        """关闭设备"""
        device = None
        if isinstance(device_identifier, str):
            device = self.get_configured_device_by_serial(device_identifier)
        
        if device is None:
            return -1
        
        return device.close()

    def close_all(self):
        """关闭所有设备"""
        for device in self.devices:
            if device.is_opened:
                device.close()

    def start_grabbing(self, device_identifier) -> int:
        """开始取流"""
        device = None
        if isinstance(device_identifier, str):
            device = self.get_configured_device_by_serial(device_identifier)
        
        if device is None:
            return -1
        
        return device.start_grabbing()

    def stop_grabbing(self, device_identifier) -> int:
        """停止取流"""
        device = None
        if isinstance(device_identifier, str):
            device = self.get_configured_device_by_serial(device_identifier)
        
        if device is None:
            return -1
        
        return device.stop_grabbing()

    def set_trigger_mode(self, device_identifier, is_trigger_mode: bool) -> int:
        """设置触发模式"""
        device = None
        if isinstance(device_identifier, str):
            device = self.get_configured_device_by_serial(device_identifier)
        
        if device is None:
            return -1
        
        return device.set_trigger_mode(is_trigger_mode)

    def trigger_software(self, device_identifier) -> int:
        """软件触发一次"""
        device = None
        if isinstance(device_identifier, str):
            device = self.get_configured_device_by_serial(device_identifier)
        
        if device is None:
            return -1
        
        return device.trigger_software()

    def get_frame(self, device_identifier, timeout: int = 1000) -> Optional[Dict[str, Any]]:
        """获取一帧图像"""
        device = None
        if isinstance(device_identifier, str):
            device = self.get_configured_device_by_serial(device_identifier)
        
        if device is None:
            return None
        
        return device.get_frame(timeout)

    def get_frame_bgr(self, device_identifier, timeout: int = 1000) -> Optional[Dict[str, Any]]:
        """获取BGR格式的一帧图像"""
        return self.get_frame(device_identifier, timeout)

    def set_float_value(self, device_identifier, param_name: str, value: float) -> int:
        """设置浮点型参数值"""
        device = None
        if isinstance(device_identifier, str):
            device = self.get_configured_device_by_serial(device_identifier)
        
        if device is None:
            return -1
        
        return device.set_float_value(param_name, value)

    def set_bool_value(self, device_identifier, param_name: str, value: bool) -> int:
        """设置布尔型参数值"""
        device = None
        if isinstance(device_identifier, str):
            device = self.get_configured_device_by_serial(device_identifier)
        
        if device is None:
            return -1
        
        return device.set_bool_value(param_name, value)

    def set_enum_value(self, device_identifier, param_name: str, value: int) -> int:
        """设置枚举型参数值"""
        device = None
        if isinstance(device_identifier, str):
            device = self.get_configured_device_by_serial(device_identifier)
        
        if device is None:
            return -1
        
        return device.set_enum_value(param_name, value)

    # 配置管理方法
    def add_camera_config(self, config: Dict[str, Any]) -> bool:
        """添加相机配置"""
        if not config.get("serial_number") or not config.get("name"):
            return False
        
        # 检查序列号是否已存在
        for existing_config in self.camera_configs:
            if existing_config.get("serial_number") == config.get("serial_number"):
                return False
        
        # 添加默认值
        default_config = {
            "id": f"CAMERA_{len(self.camera_configs) + 1:03d}",
            "name": config.get("name", ""),
            "serial_number": config.get("serial_number", ""),
            "enabled": config.get("enabled", True),
            "exposure_time": config.get("exposure_time", 10000),
            "gain": config.get("gain", 10.0)
        }
        
        self.camera_configs.append(default_config)
        
        # 更新配置管理器
        if self.config_manager:
            self.config_manager.set("camera.devices", self.camera_configs)
            self.config_manager.save()
        
        return True

    def remove_camera_config(self, serial_number: str) -> bool:
        """删除相机配置"""
        for i, config in enumerate(self.camera_configs):
            if config.get("serial_number") == serial_number:
                del self.camera_configs[i]
                
                # 更新配置管理器
                if self.config_manager:
                    self.config_manager.set("camera.devices", self.camera_configs)
                    self.config_manager.save()
                
                return True
        return False

    def update_camera_config(self, serial_number: str, updates: Dict[str, Any]) -> bool:
        """更新相机配置"""
        for config in self.camera_configs:
            if config.get("serial_number") == serial_number:
                config.update(updates)
                
                # 更新配置管理器
                if self.config_manager:
                    self.config_manager.set("camera.devices", self.camera_configs)
                    self.config_manager.save()
                
                return True
        return False

    def get_camera_configs(self) -> List[Dict[str, Any]]:
        """获取所有相机配置"""
        return self.camera_configs.copy()

    def reload_configs(self):
        """重新加载配置"""
        self._load_camera_configs()
        # 重新筛选设备
        if self.devices:
            self._filter_configured_devices()
