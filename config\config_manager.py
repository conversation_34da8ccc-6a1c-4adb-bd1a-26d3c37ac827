import json
import yaml
import threading
from typing import Any, Dict, Callable, List, Optional, Union
from pathlib import Path
import copy


class ConfigManager:
    """
    配置管理器类，支持JSON和YAML格式的配置文件读写，
    提供线程安全的访问接口和配置变更监听机制。
    """

    def __init__(
        self, config_file: str = "config.json", default_config_file: str = None
    ):
        """
        初始化配置管理器

        Args:
            config_file: 配置文件路径
            default_config_file: 默认配置文件路径
        """
        self._config_file = Path(config_file)
        self._default_config_file = (
            Path(default_config_file) if default_config_file else None
        )
        self._config = {}
        self._lock = threading.RLock()  # 使用可重入锁
        self._listeners = {}  # 配置变更监听器 {key: [callback1, callback2, ...]}

        # 加载配置
        self.reload()

    def _load_config(self) -> Dict[str, Any]:
        """
        加载配置文件

        Returns:
            配置字典
        """
        config = {}

        # 如果配置文件存在，尝试加载
        if self._config_file.exists():
            try:
                with open(self._config_file, "r", encoding="utf-8") as f:
                    if (
                        self._config_file.suffix.lower() == ".yaml"
                        or self._config_file.suffix.lower() == ".yml"
                    ):
                        config = yaml.safe_load(f) or {}
                    else:
                        config = json.load(f)
            except Exception as e:
                print(f"加载配置文件失败: {e}")

        # 如果没有配置文件或加载失败，尝试加载默认配置
        if (
            not config
            and self._default_config_file
            and self._default_config_file.exists()
        ):
            try:
                with open(self._default_config_file, "r", encoding="utf-8") as f:
                    if (
                        self._default_config_file.suffix.lower() == ".yaml"
                        or self._default_config_file.suffix.lower() == ".yml"
                    ):
                        config = yaml.safe_load(f) or {}
                    else:
                        config = json.load(f)
            except Exception as e:
                print(f"加载默认配置文件失败: {e}")

        return config or {}

    def _save_config(self, config: Dict[str, Any]) -> None:
        """
        保存配置到文件

        Args:
            config: 配置字典
        """
        # 确保配置文件目录存在
        self._config_file.parent.mkdir(parents=True, exist_ok=True)

        try:
            with open(self._config_file, "w", encoding="utf-8") as f:
                if (
                    self._config_file.suffix.lower() == ".yaml"
                    or self._config_file.suffix.lower() == ".yml"
                ):
                    yaml.dump(config, f, allow_unicode=True, indent=2)
                else:
                    json.dump(config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置项的值

        Args:
            key: 配置项键，支持点号分隔的嵌套键，如 "system.name"
            default: 默认值

        Returns:
            配置项的值或默认值
        """
        with self._lock:
            keys = key.split(".")
            value = self._config

            try:
                for k in keys:
                    value = value[k]
                return value
            except (KeyError, TypeError):
                return default

    def set(self, key: str, value: Any) -> None:
        """
        设置配置项的值

        Args:
            key: 配置项键，支持点号分隔的嵌套键，如 "system.name"
            value: 配置项的值
        """
        with self._lock:
            keys = key.split(".")
            config = self._config

            # 导航到倒数第二层
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]

            # 设置值
            old_value = config.get(keys[-1])
            config[keys[-1]] = value

            # 通知监听器
            self._notify_listeners(key, old_value, value)

    def update(self, config_dict: Dict[str, Any]) -> None:
        """
        批量更新配置

        Args:
            config_dict: 配置字典
        """
        with self._lock:
            # 深拷贝当前配置以备通知使用
            old_config = copy.deepcopy(self._config)

            # 更新配置
            self._update_dict(self._config, config_dict)

            # 通知所有变更
            self._notify_all_listeners(old_config, self._config)

    def _update_dict(self, target: Dict, source: Dict) -> None:
        """
        递归更新字典

        Args:
            target: 目标字典
            source: 源字典
        """
        for key, value in source.items():
            if (
                key in target
                and isinstance(target[key], dict)
                and isinstance(value, dict)
            ):
                self._update_dict(target[key], value)
            else:
                target[key] = value

    def reload(self) -> None:
        """
        重新加载配置文件
        """
        with self._lock:
            # 保存旧配置以备通知使用
            old_config = copy.deepcopy(self._config)

            # 加载新配置
            self._config = self._load_config()

            # 通知所有变更
            self._notify_all_listeners(old_config, self._config)

    def save(self) -> None:
        """
        保存配置到文件
        """
        with self._lock:
            self._save_config(self._config)

    def register_listener(
        self, key: str, callback: Callable[[str, Any, Any], None]
    ) -> None:
        """
        注册配置变更监听器

        Args:
            key: 配置项键
            callback: 回调函数，参数为(key, old_value, new_value)
        """
        with self._lock:
            if key not in self._listeners:
                self._listeners[key] = []
            self._listeners[key].append(callback)

    def _notify_listeners(self, key: str, old_value: Any, new_value: Any) -> None:
        """
        通知监听器配置变更

        Args:
            key: 配置项键
            old_value: 旧值
            new_value: 新值
        """
        # 通知特定键的监听器
        if key in self._listeners:
            for callback in self._listeners[key]:
                try:
                    callback(key, old_value, new_value)
                except Exception as e:
                    print(f"通知监听器时出错: {e}")

        # 通知全局监听器（键为""的监听器）
        if "" in self._listeners:
            for callback in self._listeners[""]:
                try:
                    callback(key, old_value, new_value)
                except Exception as e:
                    print(f"通知全局监听器时出错: {e}")

    def _notify_all_listeners(
        self, old_config: Dict[str, Any], new_config: Dict[str, Any]
    ) -> None:
        """
        通知所有配置变更

        Args:
            old_config: 旧配置
            new_config: 新配置
        """
        # 找出变更的键
        changed_keys = self._find_changed_keys(old_config, new_config)

        # 通知变更
        for key in changed_keys:
            old_value = self._get_nested_value(old_config, key)
            new_value = self._get_nested_value(new_config, key)
            self._notify_listeners(key, old_value, new_value)

    def _find_changed_keys(
        self, old_dict: Dict[str, Any], new_dict: Dict[str, Any], prefix: str = ""
    ) -> List[str]:
        """
        找出两个字典中不同的键

        Args:
            old_dict: 旧字典
            new_dict: 新字典
            prefix: 键前缀

        Returns:
            变更的键列表
        """
        changed_keys = []

        # 检查新字典中的键
        for key, new_value in new_dict.items():
            full_key = f"{prefix}.{key}" if prefix else key

            if key not in old_dict:
                # 新增的键
                changed_keys.append(full_key)
            else:
                old_value = old_dict[key]
                if isinstance(new_value, dict) and isinstance(old_value, dict):
                    # 递归检查嵌套字典
                    changed_keys.extend(
                        self._find_changed_keys(old_value, new_value, full_key)
                    )
                elif new_value != old_value:
                    # 值发生变化的键
                    changed_keys.append(full_key)

        # 检查旧字典中被删除的键
        for key in old_dict:
            if key not in new_dict:
                full_key = f"{prefix}.{key}" if prefix else key
                changed_keys.append(full_key)

        return changed_keys

    def _get_nested_value(self, config: Dict[str, Any], key: str) -> Any:
        """
        获取嵌套字典中的值

        Args:
            config: 配置字典
            key: 点号分隔的键

        Returns:
            配置项的值
        """
        keys = key.split(".")
        value = config

        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return None

    def validate(self, schema: Dict[str, Any]) -> bool:
        """
        验证配置是否符合指定的模式

        Args:
            schema: 验证模式

        Returns:
            验证是否通过
        """
        # 简单的验证实现，实际项目中可能需要更复杂的验证逻辑
        # 这里只是示例，可以根据需要扩展
        with self._lock:
            return self._validate_dict(self._config, schema)

    def _validate_dict(self, config: Dict[str, Any], schema: Dict[str, Any]) -> bool:
        """
        递归验证字典是否符合模式

        Args:
            config: 配置字典
            schema: 验证模式

        Returns:
            验证是否通过
        """
        for key, requirements in schema.items():
            if key not in config:
                # 检查是否为必需字段
                if isinstance(requirements, dict) and requirements.get(
                    "required", False
                ):
                    return False
                continue

            value = config[key]

            # 检查类型
            if isinstance(requirements, dict) and "type" in requirements:
                if not isinstance(value, requirements["type"]):
                    return False

            # 递归验证嵌套字典
            if (
                isinstance(value, dict)
                and isinstance(requirements, dict)
                and "schema" in requirements
            ):
                if not self._validate_dict(value, requirements["schema"]):
                    return False

        return True
