from __future__ import annotations

from dataclasses import dataclass
from typing import Optional

from config.config_manager import ConfigManager
from log.logger import Logger
from camera.camera_manager import CameraManager
from plc.plc_handler import PlcHandler
from detector.manager import DetectorManager
from data.manager import DataManager


@dataclass
class AppContext:
    """应用级上下文，集中管理各类服务与其生命周期。"""

    config_manager: ConfigManager
    logger: Logger
    camera_manager: CameraManager
    plc_handler: Optional[PlcHandler]
    detector_manager: DetectorManager
    data_manager: DataManager

    def shutdown(self) -> None:
        """统一关闭所有资源，按依赖顺序安全收口。"""
        try:
            # 相机相关
            try:
                if self.camera_manager:
                    self.camera_manager.close_all()
            except Exception as _:
                pass

            # PLC 断开
            try:
                if self.plc_handler:
                    self.plc_handler.disconnect()
            except Exception as _:
                pass

            # 检测线程池
            try:
                if self.detector_manager and getattr(
                    self.detector_manager, "executor", None
                ):
                    self.detector_manager.shutdown(wait=False)
            except Exception as _:
                pass
        finally:
            try:
                if self.logger:
                    self.logger.info("系统正常关闭")
            except Exception:
                pass
