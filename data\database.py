import sqlite3
import threading
import os
from typing import List, Dict, Any, Optional
from contextlib import contextmanager
from .types import DetectionResultWithDB
import json


class DatabaseManager:
    """数据库管理类，负责SQLite数据库的连接和操作"""

    def __init__(self, db_path: str = "detection_results.db"):
        """
        初始化数据库管理器

        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.lock = threading.Lock()
        self.init_database()

    def init_database(self) -> None:
        """初始化数据库表结构"""
        with self.get_connection() as conn:
            cursor = conn.cursor()

            # 创建检测结果表
            cursor.execute(
                """
                CREATE TABLE IF NOT EXISTS detection_results (
                    task_id TEXT PRIMARY KEY,
                    camera_id TEXT NOT NULL,
                    detector_name TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    image_path TEXT,
                    image_width INTEGER,
                    image_height INTEGER,
                    is_defect INTEGER,
                    defect_count INTEGER,
                    defects TEXT,
                    confidence REAL,
                    process_time REAL,
                    model_version TEXT,
                    algorithm_params TEXT,
                    status TEXT,
                    error_message TEXT
                )
            """
            )

            # 创建索引以提高查询性能
            cursor.execute(
                """
                CREATE INDEX IF NOT EXISTS idx_camera_id ON detection_results(camera_id)
            """
            )

            cursor.execute(
                """
                CREATE INDEX IF NOT EXISTS idx_timestamp ON detection_results(timestamp)
            """
            )

            cursor.execute(
                """
                CREATE INDEX IF NOT EXISTS idx_detector_name ON detection_results(detector_name)
            """
            )

            cursor.execute(
                """
                CREATE INDEX IF NOT EXISTS idx_status ON detection_results(status)
            """
            )

            conn.commit()

    @contextmanager
    def get_connection(self):
        """
        获取数据库连接的上下文管理器

        Yields:
            sqlite3.Connection: 数据库连接
        """
        with self.lock:
            conn = sqlite3.connect(self.db_path)
            try:
                yield conn
                conn.commit()
            except Exception:
                conn.rollback()
                raise
            finally:
                conn.close()

    def save_result(self, result: DetectionResultWithDB) -> bool:
        """
        保存检测结果

        Args:
            result: 检测结果对象

        Returns:
            bool: 是否保存成功
        """
        try:
            result_dict = result.to_dict()

            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 使用INSERT OR REPLACE来处理重复的task_id
                cursor.execute(
                    """
                    INSERT OR REPLACE INTO detection_results (
                        task_id, camera_id, detector_name, timestamp, image_path,
                        image_width, image_height, is_defect, defect_count, defects,
                        confidence, process_time, model_version, algorithm_params,
                        status, error_message
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """,
                    (
                        result_dict["task_id"],
                        result_dict["camera_id"],
                        result_dict["detector_name"],
                        result_dict["timestamp"],
                        result_dict["image_path"],
                        result_dict["image_width"],
                        result_dict["image_height"],
                        int(result_dict["is_defect"]),
                        result_dict["defect_count"],
                        result_dict["defects"],
                        result_dict["confidence"],
                        result_dict["process_time"],
                        result_dict["model_version"],
                        result_dict["algorithm_params"],
                        result_dict["status"],
                        result_dict["error_message"],
                    ),
                )

            return True
        except Exception as e:
            print(f"保存检测结果时出错: {e}")
            return False

    def get_result(self, task_id: str) -> Optional[DetectionResultWithDB]:
        """
        根据任务ID获取检测结果

        Args:
            task_id: 任务ID

        Returns:
            DetectionResultWithDB: 检测结果对象，如果未找到则返回None
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    SELECT * FROM detection_results WHERE task_id = ?
                """,
                    (task_id,),
                )

                row = cursor.fetchone()
                if row:
                    # 将行数据转换为字典
                    columns = [description[0] for description in cursor.description]
                    result_dict = dict(zip(columns, row))
                    return DetectionResultWithDB.from_dict(result_dict)

            return None
        except Exception as e:
            print(f"获取检测结果时出错: {e}")
            return None

    def query_results(
        self, filters: Dict[str, Any] = None, limit: int = 100, offset: int = 0
    ) -> List[DetectionResultWithDB]:
        """
        查询检测结果

        Args:
            filters: 查询过滤条件
            limit: 返回结果数量限制
            offset: 偏移量

        Returns:
            List[DetectionResultWithDB]: 检测结果列表
        """
        try:
            query = "SELECT * FROM detection_results WHERE 1=1"
            params = []

            if filters:
                # 处理时间范围过滤
                if "start_time" in filters:
                    query += " AND timestamp >= ?"
                    params.append(filters["start_time"])

                if "end_time" in filters:
                    query += " AND timestamp <= ?"
                    params.append(filters["end_time"])

                # 处理其他过滤条件
                for key, value in filters.items():
                    if key not in ["start_time", "end_time"]:
                        query += f" AND {key} = ?"
                        params.append(value)

            # 添加排序和分页
            query += " ORDER BY timestamp DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])

            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(query, params)

                results = []
                rows = cursor.fetchall()
                if rows:
                    columns = [description[0] for description in cursor.description]
                    for row in rows:
                        result_dict = dict(zip(columns, row))
                        results.append(DetectionResultWithDB.from_dict(result_dict))

                return results
        except Exception as e:
            print(f"查询检测结果时出错: {e}")
            return []

    def delete_result(self, task_id: str) -> bool:
        """
        删除检测结果

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否删除成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    DELETE FROM detection_results WHERE task_id = ?
                """,
                    (task_id,),
                )

                return cursor.rowcount > 0
        except Exception as e:
            print(f"删除检测结果时出错: {e}")
            return False

    def clear_all_results(self) -> bool:
        """
        清空所有检测结果

        Returns:
            bool: 是否清空成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(
                    """
                    DELETE FROM detection_results
                """
                )

            return True
        except Exception as e:
            print(f"清空检测结果时出错: {e}")
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()

                # 总检测数量
                cursor.execute(
                    """
                    SELECT COUNT(*) FROM detection_results
                """
                )
                total_count = cursor.fetchone()[0]

                # 缺陷检测数量
                cursor.execute(
                    """
                    SELECT COUNT(*) FROM detection_results WHERE is_defect = 1
                """
                )
                defect_count = cursor.fetchone()[0]

                # 按相机ID统计
                cursor.execute(
                    """
                    SELECT camera_id, COUNT(*) FROM detection_results 
                    GROUP BY camera_id
                """
                )
                camera_stats = dict(cursor.fetchall())

                # 按检测器名称统计
                cursor.execute(
                    """
                    SELECT detector_name, COUNT(*) FROM detection_results 
                    GROUP BY detector_name
                """
                )
                detector_stats = dict(cursor.fetchall())

                # 按状态统计
                cursor.execute(
                    """
                    SELECT status, COUNT(*) FROM detection_results 
                    GROUP BY status
                """
                )
                status_stats = dict(cursor.fetchall())

                # 平均处理时间
                cursor.execute(
                    """
                    SELECT AVG(process_time) FROM detection_results
                """
                )
                avg_process_time = cursor.fetchone()[0]

                return {
                    "total_count": total_count,
                    "defect_count": defect_count,
                    "defect_rate": defect_count / total_count if total_count > 0 else 0,
                    "camera_stats": camera_stats,
                    "detector_stats": detector_stats,
                    "status_stats": status_stats,
                    "avg_process_time": avg_process_time or 0,
                }
        except Exception as e:
            print(f"获取统计信息时出错: {e}")
            return {}
