import csv
import json
from typing import List, Dict, Any, Optional
from pathlib import Path
from detector.types import DetectionResult
from .types import DetectionResultWithDB
from .database import DatabaseManager


class DataManager:
    """数据管理器类，负责检测数据的存储、查询和导出"""

    def __init__(self, db_path: str = "detection_results.db"):
        """
        初始化数据管理器

        Args:
            db_path: 数据库文件路径
        """
        self.db_manager = DatabaseManager(db_path)

    def save_result(self, result: DetectionResult) -> bool:
        """
        保存检测结果

        Args:
            result: 检测结果对象

        Returns:
            bool: 是否保存成功
        """
        # 将DetectionResult转换为DetectionResultWithDB
        result_with_db = DetectionResultWithDB(
            task_id=result.task_id,
            camera_id=result.camera_id,
            detector_name=result.detector_name,
            timestamp=result.timestamp,
            image_path=result.image_path,
            image_width=result.image_width,
            image_height=result.image_height,
            is_defect=result.is_defect,
            defect_count=result.defect_count,
            defects=result.defects,
            confidence=result.confidence,
            process_time=result.process_time,
            model_version=result.model_version,
            algorithm_params=result.algorithm_params,
            status=result.status,
            error_message=result.error_message,
        )

        return self.db_manager.save_result(result_with_db)

    def get_result(self, task_id: str) -> Optional[DetectionResult]:
        """
        获取检测结果

        Args:
            task_id: 任务ID

        Returns:
            DetectionResult: 检测结果对象，如果未找到则返回None
        """
        result = self.db_manager.get_result(task_id)
        return result

    def query_results(
        self, filters: Dict[str, Any] = None, limit: int = 100, offset: int = 0
    ) -> List[DetectionResult]:
        """
        查询检测结果

        Args:
            filters: 查询过滤条件
            limit: 返回结果数量限制
            offset: 偏移量

        Returns:
            List[DetectionResult]: 检测结果列表
        """
        results = self.db_manager.query_results(filters, limit, offset)
        return results

    def export_to_csv(self, file_path: str, filters: Dict[str, Any] = None) -> bool:
        """
        导出检测结果到CSV文件

        Args:
            file_path: CSV文件路径
            filters: 查询过滤条件

        Returns:
            bool: 是否导出成功
        """
        try:
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)

            # 查询数据
            results = self.db_manager.query_results(filters)

            if not results:
                # 创建空文件并写入表头
                with open(file_path, "w", newline="", encoding="utf-8") as csvfile:
                    fieldnames = [
                        "task_id",
                        "camera_id",
                        "detector_name",
                        "timestamp",
                        "image_path",
                        "image_width",
                        "image_height",
                        "is_defect",
                        "defect_count",
                        "confidence",
                        "process_time",
                        "model_version",
                        "status",
                        "error_message",
                    ]
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                return True

            # 写入CSV文件
            with open(file_path, "w", newline="", encoding="utf-8") as csvfile:
                fieldnames = [
                    "task_id",
                    "camera_id",
                    "detector_name",
                    "timestamp",
                    "image_path",
                    "image_width",
                    "image_height",
                    "is_defect",
                    "defect_count",
                    "confidence",
                    "process_time",
                    "model_version",
                    "status",
                    "error_message",
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                # 写入表头
                writer.writeheader()

                # 写入数据
                for result in results:
                    result_dict = result.to_dict()
                    # 移除不需要的字段
                    for key in ["defects", "algorithm_params"]:
                        result_dict.pop(key, None)
                    writer.writerow(result_dict)

            return True
        except Exception as e:
            print(f"导出CSV时出错: {e}")
            return False

    def export_to_excel(self, file_path: str, filters: Dict[str, Any] = None) -> bool:
        """
        导出检测结果到Excel文件

        Args:
            file_path: Excel文件路径
            filters: 查询过滤条件

        Returns:
            bool: 是否导出成功
        """
        try:
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)

            # 查询数据
            results = self.db_manager.query_results(filters)

            # 使用pandas导出Excel（如果可用）
            try:
                import pandas as pd

                if not results:
                    # 创建空的DataFrame
                    df = pd.DataFrame(
                        columns=[
                            "task_id",
                            "camera_id",
                            "detector_name",
                            "timestamp",
                            "image_path",
                            "image_width",
                            "image_height",
                            "is_defect",
                            "defect_count",
                            "confidence",
                            "process_time",
                            "model_version",
                            "status",
                            "error_message",
                        ]
                    )
                else:
                    # 转换为DataFrame
                    data = []
                    for result in results:
                        result_dict = result.to_dict()
                        # 移除不需要的字段
                        for key in ["defects", "algorithm_params"]:
                            result_dict.pop(key, None)
                        data.append(result_dict)

                    df = pd.DataFrame(data)

                # 保存到Excel
                df.to_excel(file_path, index=False, engine="openpyxl")
                return True
            except ImportError:
                # 如果pandas不可用，导出为JSON格式作为替代
                print("警告: 未安装pandas，将导出为JSON格式")
                data = [result.to_dict() for result in results] if results else []
                # 移除不需要的字段
                for item in data:
                    for key in ["defects", "algorithm_params"]:
                        item.pop(key, None)

                with open(
                    file_path.replace(".xlsx", ".json"), "w", encoding="utf-8"
                ) as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)

                return True
        except Exception as e:
            print(f"导出Excel时出错: {e}")
            return False

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息

        Returns:
            Dict[str, Any]: 统计信息字典
        """
        return self.db_manager.get_statistics()

    def delete_result(self, task_id: str) -> bool:
        """
        删除检测结果

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否删除成功
        """
        return self.db_manager.delete_result(task_id)

    def clear_all_results(self) -> bool:
        """
        清空所有检测结果

        Returns:
            bool: 是否清空成功
        """
        return self.db_manager.clear_all_results()
