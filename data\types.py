from dataclasses import dataclass, asdict
from typing import List, Dict, Any, Optional
from detector.types import DetectionResult, DefectInfo, BoundingBox
import json


@dataclass
class DetectionResultWithDB(DetectionResult):
    """扩展的检测结果类，用于数据库存储"""

    def to_dict(self) -> Dict[str, Any]:
        """
        将检测结果转换为字典，用于数据库存储

        Returns:
            Dict[str, Any]: 检测结果字典
        """
        result_dict = asdict(self)
        # 将缺陷列表转换为JSON字符串存储
        result_dict["defects"] = json.dumps(
            [asdict(defect) for defect in self.defects], ensure_ascii=False
        )
        # 将算法参数转换为JSON字符串存储
        result_dict["algorithm_params"] = json.dumps(
            self.algorithm_params, ensure_ascii=False
        )
        return result_dict

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "DetectionResultWithDB":
        """
        从字典创建检测结果对象

        Args:
            data: 检测结果字典

        Returns:
            DetectionResultWithDB: 检测结果对象
        """
        # 复制数据以避免修改原始数据
        result_data = data.copy()

        # 将缺陷列表从JSON字符串转换回对象
        if "defects" in result_data and isinstance(result_data["defects"], str):
            try:
                defects_data = json.loads(result_data["defects"])
                result_data["defects"] = [
                    DefectInfo(
                        defect_type=d["defect_type"],
                        confidence=d["confidence"],
                        bounding_box=BoundingBox(**d["bounding_box"]),
                        area_ratio=d["area_ratio"],
                        severity=d["severity"],
                        description=d["description"],
                    )
                    for d in defects_data
                ]
            except (json.JSONDecodeError, KeyError, TypeError):
                result_data["defects"] = []

        # 将算法参数从JSON字符串转换回字典
        if "algorithm_params" in result_data and isinstance(
            result_data["algorithm_params"], str
        ):
            try:
                result_data["algorithm_params"] = json.loads(
                    result_data["algorithm_params"]
                )
            except (json.JSONDecodeError, TypeError):
                result_data["algorithm_params"] = {}

        # 移除不在构造函数中的字段
        field_names = {f.name for f in cls.__dataclass_fields__.values()}
        filtered_data = {k: v for k, v in result_data.items() if k in field_names}

        return cls(**filtered_data)
