from abc import ABC, abstractmethod
from typing import Any
from .types import DetectionResult


class DetectorBase(ABC):
    """检测算法基类，定义标准检测接口"""

    def __init__(self, name: str, model_path: str = None):
        """
        初始化检测器

        Args:
            name: 检测器名称
            model_path: 模型文件路径
        """
        self.name = name
        self.model_path = model_path
        self.model_loaded = False

    @abstractmethod
    def load_model(self) -> bool:
        """
        加载模型

        Returns:
            bool: 是否成功加载模型
        """
        pass

    @abstractmethod
    def detect(self, image_path: str, **kwargs) -> DetectionResult:
        """
        执行检测

        Args:
            image_path: 图像文件路径
            **kwargs: 其他参数

        Returns:
            DetectionResult: 检测结果
        """
        pass

    @abstractmethod
    def unload_model(self) -> bool:
        """
        卸载模型

        Returns:
            bool: 是否成功卸载模型
        """
        pass
