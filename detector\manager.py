import threading
import time
import uuid
from typing import Dict, Optional, List
from concurrent.futures import ThreadPoolExecutor, Future
from .base import DetectorBase
from .types import DetectionResult


class DetectorManager:
    """检测管理器，负责后台调用检测算法，调度多线程检测任务"""

    def __init__(self, max_workers: int = 4):
        """
        初始化检测管理器

        Args:
            max_workers: 最大工作线程数
        """
        self.detectors: Dict[str, DetectorBase] = {}
        self.tasks: Dict[str, Future] = {}
        self.results: Dict[str, DetectionResult] = {}
        self.task_status: Dict[str, str] = (
            {}
        )  # pending, running, completed, cancelled, error
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.lock = threading.Lock()

    def register_detector(self, detector: DetectorBase) -> bool:
        """
        注册检测器

        Args:
            detector: 检测器实例

        Returns:
            bool: 是否成功注册
        """
        with self.lock:
            if detector.name in self.detectors:
                return False
            self.detectors[detector.name] = detector
            return True

    def submit_task(
        self, camera_id: str, image_path: str, detector_name: str = None, **kwargs
    ) -> str:
        """
        提交检测任务

        Args:
            camera_id: 相机ID
            image_path: 图像文件路径
            detector_name: 检测器名称，如果为None则使用第一个注册的检测器
            **kwargs: 其他参数

        Returns:
            str: 任务ID
        """
        task_id = str(uuid.uuid4())

        # 如果没有指定检测器，则使用第一个注册的检测器
        if detector_name is None:
            if not self.detectors:
                raise ValueError("No detectors registered")
            detector_name = next(iter(self.detectors))

        # 检查检测器是否存在
        if detector_name not in self.detectors:
            raise ValueError(f"Detector {detector_name} not found")

        detector = self.detectors[detector_name]

        # 提交任务到线程池
        with self.lock:
            self.task_status[task_id] = "pending"
            future = self.executor.submit(
                self._run_detection, task_id, detector, camera_id, image_path, **kwargs
            )
            self.tasks[task_id] = future

        return task_id

    def _run_detection(
        self,
        task_id: str,
        detector: DetectorBase,
        camera_id: str,
        image_path: str,
        **kwargs,
    ) -> DetectionResult:
        """
        执行检测任务（在线程池中运行）

        Args:
            task_id: 任务ID
            detector: 检测器实例
            camera_id: 相机ID
            image_path: 图像文件路径
            **kwargs: 其他参数

        Returns:
            DetectionResult: 检测结果
        """
        with self.lock:
            self.task_status[task_id] = "running"

        try:
            # 执行检测
            start_time = time.time()
            result = detector.detect(image_path, **kwargs)
            process_time = (time.time() - start_time) * 1000  # 转换为毫秒

            # 更新结果信息
            result.task_id = task_id
            result.camera_id = camera_id
            result.detector_name = detector.name
            result.process_time = process_time
            result.status = "success"
            result.error_message = ""

            # 保存结果
            with self.lock:
                self.results[task_id] = result
                self.task_status[task_id] = "completed"

            return result
        except Exception as e:
            # 处理错误情况
            error_result = DetectionResult(
                task_id=task_id,
                camera_id=camera_id,
                detector_name=detector.name,
                timestamp=time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                image_path=image_path,
                image_width=0,
                image_height=0,
                is_defect=False,
                defect_count=0,
                defects=[],
                confidence=0.0,
                process_time=0.0,
                model_version="",
                algorithm_params={},
                status="error",
                error_message=str(e),
            )

            with self.lock:
                self.results[task_id] = error_result
                self.task_status[task_id] = "error"

            return error_result

    def get_result(self, task_id: str) -> Optional[DetectionResult]:
        """
        获取检测结果

        Args:
            task_id: 任务ID

        Returns:
            DetectionResult: 检测结果，如果任务未完成则返回None
        """
        with self.lock:
            return self.results.get(task_id, None)

    def cancel_task(self, task_id: str) -> bool:
        """
        取消检测任务

        Args:
            task_id: 任务ID

        Returns:
            bool: 是否成功取消
        """
        with self.lock:
            if task_id not in self.tasks:
                return False

            future = self.tasks[task_id]
            cancelled = future.cancel()

            if cancelled:
                self.task_status[task_id] = "cancelled"

            return cancelled

    def get_task_status(self, task_id: str) -> str:
        """
        获取任务状态

        Args:
            task_id: 任务ID

        Returns:
            str: 任务状态 (pending, running, completed, cancelled, error)
        """
        with self.lock:
            return self.task_status.get(task_id, "unknown")

    def cleanup_results(self, max_age: float = 3600) -> int:
        """
        清理旧的检测结果

        Args:
            max_age: 最大保留时间（秒）

        Returns:
            int: 清理的结果数量
        """
        current_time = time.time()
        cleaned_count = 0

        with self.lock:
            # 找到需要清理的任务
            to_remove = []
            for task_id, result in self.results.items():
                # 简单的时间检查（这里假设result.timestamp是可解析的时间戳）
                # 在实际应用中，可能需要更精确的时间比较
                if hasattr(result, "timestamp"):
                    try:
                        # 这里简化处理，实际应用中需要解析ISO时间戳
                        # 如果结果超过max_age秒，则清理
                        if current_time - time.time() > max_age:  # 这里简化处理
                            to_remove.append(task_id)
                    except:
                        # 如果时间解析失败，跳过该结果
                        pass

            # 清理结果
            for task_id in to_remove:
                if task_id in self.results:
                    del self.results[task_id]
                    cleaned_count += 1

                if task_id in self.tasks:
                    del self.tasks[task_id]

                if task_id in self.task_status:
                    del self.task_status[task_id]

        return cleaned_count

    def shutdown(self, wait: bool = True):
        """
        关闭检测管理器

        Args:
            wait: 是否等待所有任务完成
        """
        self.executor.shutdown(wait=wait)
