from dataclasses import dataclass
from typing import List, Dict, Any


@dataclass
class BoundingBox:
    """边界框"""

    x: int  # 左上角x坐标
    y: int  # 左上角y坐标
    width: int  # 宽度
    height: int  # 高度


@dataclass
class DefectInfo:
    """缺陷信息"""

    defect_type: str  # 缺陷类型
    confidence: float  # 置信度 (0.0-1.0)
    bounding_box: BoundingBox  # 边界框
    area_ratio: float  # 缺陷面积占比
    severity: str  # 严重程度 (low, medium, high, critical)
    description: str  # 缺陷描述


@dataclass
class DetectionResult:
    """检测结果"""

    # 基本信息
    task_id: str  # 任务ID
    camera_id: str  # 相机ID
    detector_name: str  # 检测器名称
    timestamp: str  # 时间戳 (ISO格式)

    # 图像信息
    image_path: str  # 图像文件路径
    image_width: int  # 图像宽度
    image_height: int  # 图像高度

    # 检测结果
    is_defect: bool  # 是否为缺陷
    defect_count: int  # 缺陷数量
    defects: List[DefectInfo]  # 缺陷列表
    confidence: float  # 整体置信度

    # 处理信息
    process_time: float  # 处理时间(毫秒)
    model_version: str  # 模型版本
    algorithm_params: Dict[str, Any]  # 算法参数

    # 状态信息
    status: str  # 处理状态 (success, error, timeout)
    error_message: str  # 错误信息 (如果有)
