"""
日志格式化器模块
提供日志格式化功能，支持自定义格式
"""

import time
from typing import Any, Dict
from enum import IntEnum


class LogLevel(IntEnum):
    """日志级别枚举"""

    DEBUG = 10
    INFO = 20
    WARNING = 30
    ERROR = 40


class LogFormatter:
    """基础日志格式化器"""

    def format(self, record: Dict[str, Any]) -> str:
        """
        格式化日志记录

        Args:
            record: 日志记录字典

        Returns:
            格式化后的日志字符串
        """
        raise NotImplementedError


class DefaultFormatter(LogFormatter):
    """默认日志格式化器"""

    def __init__(self, fmt: str = None):
        """
        初始化格式化器

        Args:
            fmt: 格式化字符串，默认为 "[%(asctime)s] [%(levelname)s] [%(module)s] %(message)s"
        """
        self.fmt = fmt or "[%(asctime)s] [%(levelname)s] [%(module)s] %(message)s"

    def format(self, record: Dict[str, Any]) -> str:
        """
        格式化日志记录

        Args:
            record: 日志记录字典

        Returns:
            格式化后的日志字符串
        """
        # 转换时间戳为可读格式
        if "timestamp" in record:
            record["asctime"] = time.strftime(
                "%Y-%m-%d %H:%M:%S", time.localtime(record["timestamp"])
            )

        # 转换日志级别为字符串
        if "level" in record:
            record["levelname"] = LogLevel(record["level"]).name

        # 格式化消息
        formatted = self.fmt % record
        return formatted


class JsonFormatter(LogFormatter):
    """JSON格式日志格式化器"""

    def format(self, record: Dict[str, Any]) -> str:
        """
        格式化日志记录为JSON格式

        Args:
            record: 日志记录字典

        Returns:
            JSON格式的日志字符串
        """
        import json

        # 转换时间戳为可读格式
        if "timestamp" in record:
            record["asctime"] = time.strftime(
                "%Y-%m-%d %H:%M:%S", time.localtime(record["timestamp"])
            )

        # 转换日志级别为字符串
        if "level" in record:
            record["levelname"] = LogLevel(record["level"]).name

        return json.dumps(record, ensure_ascii=False)
