"""
日志处理器模块
提供不同类型的日志处理器，如文件处理器、UI处理器等
"""

import os
import threading
import queue
from typing import Any, Dict, List, Callable
from pathlib import Path
from log.formatters import LogFormatter, DefaultFormatter


class LogHandler:
    """基础日志处理器"""

    def __init__(self, formatter: LogFormatter = None):
        """
        初始化日志处理器

        Args:
            formatter: 日志格式化器
        """
        self.formatter = formatter or DefaultFormatter()

    def emit(self, record: Dict[str, Any]) -> None:
        """
        输出日志记录

        Args:
            record: 日志记录字典
        """
        raise NotImplementedError


class FileHandler(LogHandler):
    """文件日志处理器"""

    def __init__(
        self,
        filename: str,
        formatter: LogFormatter = None,
        max_bytes: int = 10 * 1024 * 1024,
        backup_count: int = 5,
    ):
        """
        初始化文件日志处理器

        Args:
            filename: 日志文件名
            formatter: 日志格式化器
            max_bytes: 日志文件最大字节数（默认10MB）
            backup_count: 保留的历史文件数量（默认5个）
        """
        super().__init__(formatter)
        self.filename = Path(filename)
        self.max_bytes = max_bytes
        self.backup_count = backup_count

        # 确保日志目录存在
        self.filename.parent.mkdir(parents=True, exist_ok=True)

        # 线程安全锁
        self._lock = threading.Lock()

    def emit(self, record: Dict[str, Any]) -> None:
        """
        将日志记录写入文件

        Args:
            record: 日志记录字典
        """
        with self._lock:
            # 检查是否需要轮转
            if self.should_rollover(record):
                self.do_rollover()

            # 格式化日志记录
            formatted = self.formatter.format(record)

            # 写入文件
            try:
                with open(self.filename, "a", encoding="utf-8") as f:
                    f.write(formatted + "\n")
            except Exception as e:
                # 如果写入失败，输出到标准错误（避免递归）
                import sys

                print(f"写入日志文件失败: {e}", file=sys.stderr)

    def should_rollover(self, record: Dict[str, Any]) -> bool:
        """
        判断是否需要轮转日志文件

        Args:
            record: 日志记录字典

        Returns:
            是否需要轮转
        """
        if not self.filename.exists():
            return False

        try:
            # 检查文件大小
            file_size = self.filename.stat().st_size
            formatted = self.formatter.format(record)
            return file_size + len(formatted) > self.max_bytes
        except Exception:
            return False

    def do_rollover(self) -> None:
        """
        执行日志文件轮转
        """
        # 关闭当前文件
        if self.filename.exists():
            # 重命名现有文件
            for i in range(self.backup_count - 1, 0, -1):
                src = self.filename.with_suffix(f".{i}{self.filename.suffix}")
                dst = self.filename.with_suffix(f".{i+1}{self.filename.suffix}")
                if src.exists():
                    if dst.exists():
                        dst.unlink()
                    src.rename(dst)

            # 重命名当前日志文件
            dst = self.filename.with_suffix(f".1{self.filename.suffix}")
            if dst.exists():
                dst.unlink()
            self.filename.rename(dst)


class UIHandler(LogHandler):
    """UI日志处理器"""

    def __init__(self, formatter: LogFormatter = None):
        """
        初始化UI日志处理器

        Args:
            formatter: 日志格式化器
        """
        super().__init__(formatter)
        self.callbacks: List[Callable[[str], None]] = []
        self._lock = threading.Lock()

    def add_callback(self, callback: Callable[[str], None]) -> None:
        """
        添加UI回调函数

        Args:
            callback: 回调函数，接收格式化后的日志字符串
        """
        with self._lock:
            self.callbacks.append(callback)

    def remove_callback(self, callback: Callable[[str], None]) -> None:
        """
        移除UI回调函数

        Args:
            callback: 要移除的回调函数
        """
        with self._lock:
            if callback in self.callbacks:
                self.callbacks.remove(callback)

    def emit(self, record: Dict[str, Any]) -> None:
        """
        将日志记录发送到UI

        Args:
            record: 日志记录字典
        """
        # 格式化日志记录
        formatted = self.formatter.format(record)

        # 调用所有回调函数
        with self._lock:
            for callback in self.callbacks[:]:  # 使用副本避免在迭代时修改列表
                try:
                    callback(formatted)
                except Exception as e:
                    # 如果回调函数出错，输出到标准错误（避免递归）
                    import sys

                    print(f"UI回调函数执行失败: {e}", file=sys.stderr)


class MemoryHandler(LogHandler):
    """内存日志处理器"""

    def __init__(self, formatter: LogFormatter = None, capacity: int = 1000):
        """
        初始化内存日志处理器

        Args:
            formatter: 日志格式化器
            capacity: 内存中保存的日志记录数量上限
        """
        super().__init__(formatter)
        self.capacity = capacity
        self.records: List[Dict[str, Any]] = []
        self._lock = threading.Lock()

    def emit(self, record: Dict[str, Any]) -> None:
        """
        将日志记录保存到内存

        Args:
            record: 日志记录字典
        """
        with self._lock:
            self.records.append(record)
            # 保持容量限制
            if len(self.records) > self.capacity:
                self.records.pop(0)

    def get_records(self, count: int = None) -> List[Dict[str, Any]]:
        """
        获取日志记录

        Args:
            count: 获取的记录数量，None表示获取所有记录

        Returns:
            日志记录列表
        """
        with self._lock:
            if count is None:
                return self.records.copy()
            else:
                return self.records[-count:].copy()

    def clear(self) -> None:
        """清空日志记录"""
        with self._lock:
            self.records.clear()

    def filter_by_level(self, level: int, count: int = None) -> List[Dict[str, Any]]:
        """
        根据级别过滤日志记录

        Args:
            level: 日志级别
            count: 获取的记录数量，None表示获取所有记录

        Returns:
            过滤后的日志记录列表
        """
        with self._lock:
            filtered = [
                record for record in self.records if record.get("level", 0) >= level
            ]
            if count is None:
                return filtered
            else:
                return filtered[-count:]
