"""
日志模块核心类
提供日志记录和管理功能
"""

import time
import threading
from typing import Any, Dict, List, Optional
from log.formatters import <PERSON>g<PERSON><PERSON><PERSON>, DefaultFormatter
from log.handlers import <PERSON>g<PERSON>and<PERSON>, MemoryHandler


class Logger:
    """日志记录器类"""

    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, config_manager=None):
        """
        初始化日志记录器

        Args:
            config_manager: 配置管理器实例
        """
        # 防止重复初始化
        if hasattr(self, "_initialized"):
            return

        self.config_manager = config_manager
        self.handlers: List[LogHandler] = []
        self.level = LogLevel.INFO  # 默认日志级别
        self._handler_lock = threading.Lock()

        # 添加默认的内存处理器用于获取最近日志
        self.memory_handler = MemoryHandler(capacity=1000)
        self.add_handler(self.memory_handler)

        # 从配置管理器获取日志配置
        if self.config_manager:
            self._load_config()
            # 监听配置变更
            self.config_manager.register_listener("logging", self._on_config_change)

        self._initialized = True

    def _load_config(self) -> None:
        """从配置管理器加载日志配置"""
        if not self.config_manager:
            return

        # 获取日志级别
        level_str = self.config_manager.get("logging.level", "INFO")
        try:
            self.level = LogLevel[level_str.upper()]
        except KeyError:
            self.level = LogLevel.INFO

        # 获取日志文件路径
        log_file = self.config_manager.get("logging.file.path", "app.log")
        if log_file:
            from log.handlers import FileHandler

            # 移除旧的文件处理器
            old_file_handlers = [h for h in self.handlers if isinstance(h, FileHandler)]
            for handler in old_file_handlers:
                self.remove_handler(handler)

            # 添加新的文件处理器
            file_handler = FileHandler(log_file, DefaultFormatter())
            self.add_handler(file_handler)

    def _on_config_change(self, key: str, old_value: Any, new_value: Any) -> None:
        """
        配置变更回调函数

        Args:
            key: 配置项键
            old_value: 旧值
            new_value: 新值
        """
        # 重新加载配置
        self._load_config()

    def add_handler(self, handler: LogHandler) -> None:
        """
        添加日志处理器

        Args:
            handler: 日志处理器
        """
        with self._handler_lock:
            if handler not in self.handlers:
                self.handlers.append(handler)

    def remove_handler(self, handler: LogHandler) -> None:
        """
        移除日志处理器

        Args:
            handler: 日志处理器
        """
        with self._handler_lock:
            if handler in self.handlers:
                self.handlers.remove(handler)

    def set_level(self, level: LogLevel) -> None:
        """
        设置日志级别

        Args:
            level: 日志级别
        """
        self.level = level

    def _log(self, level: LogLevel, message: str, module: str = None) -> None:
        """
        记录日志的核心方法

        Args:
            level: 日志级别
            message: 日志消息
            module: 模块名称
        """
        # 检查日志级别
        if level < self.level:
            return

        # 创建日志记录
        record = {
            "timestamp": time.time(),
            "level": level,
            "message": message,
            "module": module or "unknown",
        }

        # 发送到所有处理器
        with self._handler_lock:
            for handler in self.handlers:
                try:
                    handler.emit(record)
                except Exception as e:
                    # 避免日志记录过程中的异常影响主程序
                    pass  # 在实际应用中可能需要特殊的错误处理

    def debug(self, message: str, module: str = None) -> None:
        """
        记录DEBUG日志

        Args:
            message: 日志消息
            module: 模块名称
        """
        self._log(LogLevel.DEBUG, message, module)

    def info(self, message: str, module: str = None) -> None:
        """
        记录INFO日志

        Args:
            message: 日志消息
            module: 模块名称
        """
        self._log(LogLevel.INFO, message, module)

    def warning(self, message: str, module: str = None) -> None:
        """
        记录WARNING日志

        Args:
            message: 日志消息
            module: 模块名称
        """
        self._log(LogLevel.WARNING, message, module)

    def error(self, message: str, module: str = None) -> None:
        """
        记录ERROR日志

        Args:
            message: 日志消息
            module: 模块名称
        """
        self._log(LogLevel.ERROR, message, module)

    def get_recent_logs(self, count: int = 100) -> List[Dict[str, Any]]:
        """
        获取最近的日志记录

        Args:
            count: 获取的记录数量

        Returns:
            日志记录列表
        """
        return self.memory_handler.get_records(count)

    def get_logs_by_level(
        self, level: LogLevel, count: int = 100
    ) -> List[Dict[str, Any]]:
        """
        根据级别获取日志记录

        Args:
            level: 日志级别
            count: 获取的记录数量

        Returns:
            过滤后的日志记录列表
        """
        return self.memory_handler.filter_by_level(level, count)

    def clear_logs(self) -> None:
        """清空日志记录"""
        self.memory_handler.clear()


# 全局日志记录器实例
logger_instance = None


def get_logger(config_manager=None) -> Logger:
    """
    获取全局日志记录器实例

    Args:
        config_manager: 配置管理器实例

    Returns:
        Logger实例
    """
    global logger_instance
    if logger_instance is None:
        logger_instance = Logger(config_manager)
    return logger_instance
