import clr
import os
from typing import Tuple


class PlcHandler:
    """
    信捷 PLC 控制类
    """

    def __init__(self):
        """初始化 PLC 控制对象"""
        self.dll_dir = os.path.dirname(os.path.abspath(__file__))
        self.plc = None
        self._load_dll()

    def _load_dll(self):
        """加载 PLC 所需的 DLL 并创建 PlcControl 实例"""
        dll_dir = os.path.dirname(os.path.abspath(__file__))

        required_dlls = ["SP_SlaveContrl.dll", "SiPo_Fx3uContrl.dll", "ComFxPlc.dll"]
        missing = [dll for dll in required_dlls if not os.path.exists(os.path.join(dll_dir, dll))]
        if missing:
            raise FileNotFoundError(f"缺少必要的 DLL 文件: {missing}")

        try:
            if hasattr(os, "add_dll_directory"):
                os.add_dll_directory(dll_dir)
        except Exception:
            pass

        os.environ["PATH"] = dll_dir + os.pathsep + os.environ.get("PATH", "")

        try:
            from System.Reflection import Assembly
            Assembly.LoadFrom(os.path.join(dll_dir, "SP_SlaveContrl.dll"))
        except Exception:
            pass

        attempts = [
            "SP_SlaveContrl",
            os.path.join(dll_dir, "SP_SlaveContrl.dll")
        ]
        success = False
        for attempt in attempts:
            try:
                clr.AddReference(attempt)
                success = True
                break
            except Exception:
                continue

        if not success:
            raise RuntimeError("无法加载 SP_SlaveContrl.dll：clr.AddReference 所有尝试均失败")

        try:
            from SP_SlaveContrl import PlcControl
            self.plc = PlcControl()
        except Exception as e:
            raise RuntimeError(f"创建 PlcControl 实例失败: {e}")

    def connect(self) -> bool:
        return self.plc.SlaveLoad()

    def disconnect(self):
        self.plc.SlaveClose()
        self.plc = None

    def write_blow_time(self, ms: int) -> bool:
        return self.plc.BlowTime(ms)

    def write_trigger_interval(self, ms: int) -> bool:
        return self.plc.TriggerInterval(ms)

    def write_run_velocity(self, velocity: int) -> bool:
        return self.plc.RunVelocity(velocity)

    def confirm_view_pos(self, view_num: int, pos: int) -> bool:
        return self.plc.ViewPos_Confirm(view_num, pos)

    def confirm_view_pos_simple(self, view_num: int) -> bool:
        return self.plc.ViewPos_Confirm(view_num)

    def get_view_pos(self, view_num: int) -> Tuple[bool, int]:
        try:
            succeed, pos_value = self.plc.Get_ViewPos(view_num, 0)
            return bool(succeed), int(pos_value)
        except Exception:
            return False, 0

    def get_blow_pos(self, blow_num: int) -> Tuple[bool, int]:
        try:
            succeed, pos_value = self.plc.Get_BlowPos(blow_num, 0)
            return bool(succeed), int(pos_value)
        except Exception:
            return False, 0

    def get_velocity(self) -> Tuple[bool, int]:
        try:
            succeed, velocity = self.plc.Get_RunVelocity(0)
            return bool(succeed), int(velocity)
        except Exception:
            return False, 0

    def get_numerical_order(self, idx: int) -> Tuple[bool, int]:
        try:
            succeed, order_value = self.plc.Get_Numericalorder(idx, 0)
            return bool(succeed), int(order_value)
        except Exception:
            return False, 0

    def run(self) -> bool:
        return self.plc.Run()

    def stop(self) -> bool:
        return self.plc.Stop()

    def stop_delay(self) -> bool:
        return self.plc.Stop_Delay()

    def get_run_status(self) -> bool:
        return self.plc.Get_RunStatus()

    def open_vibrator(self, open: bool) -> bool:
        return self.plc.Run_OpenBPDK(open)

    def start_view_pos(self, view_num: int = 0) -> bool:
        return self.plc.StartViewPos(view_num)

    def continuous_view_pos(self, view_num: int = 1) -> bool:
        return self.plc.ContinuousViewPos(view_num)

    def end_view_pos(self) -> bool:
        return self.plc.EndViewPos()

    def rotate(self, on: bool) -> bool:
        return self.plc.Rotate(on)

    def rotate_slow(self, on: bool) -> bool:
        return self.plc.Rotate_Slow(on)

    def inv_rotate(self, on: bool) -> bool:
        return self.plc.InvRotate(on)

    def inv_rotate_slow(self, on: bool) -> bool:
        return self.plc.InvRotate_Slow(on)

    def blow_test(self, on: bool) -> bool:
        return self.plc.BlowTest(on)

    def submit_result(self, result: int, flag: int = 0) -> bool:
        return self.plc.Run_SubmitResult(result, flag)


if __name__ == "__main__":
    plc_handler = PlcHandler()
    if plc_handler.connect():
        ok, pos = plc_handler.get_blow_pos(0)
        print("吹气位置结果:", ok, pos)

        ok, vel = plc_handler.get_velocity()
        print("速度结果:", ok, vel)

        plc_handler.disconnect()
