from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QGridLayout,
    QLabel,
    QPushButton,
    QFrame,
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QPixmap, QImage
from qfluentwidgets import (
    ScrollArea,
    BodyLabel,
    TitleLabel,
    CardWidget,
    PushButton,
    Slider,
    ComboBox,
)
import cv2
import numpy as np
import ctypes
from camera.PixelType_header import PixelType_Gvsp_BGR8_Packed
from ui.base_page import BasePage


class CameraPage(BasePage):
    """相机预览页面"""

    def __init__(self, ctx, parent=None):
        super().__init__(ctx, parent=parent)
        self.setObjectName("CameraPage")
        self.camera_frames = {}
        self.setup_ui()
        self.initialize_cameras_soft_trigger()
        self.capture_and_display_all()

    def setup_ui(self):
        """设置UI界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        title_label = TitleLabel("相机预览")
        layout.addWidget(title_label)

        # 主要内容区域：左边大图，右边小图列表
        main_layout = QHBoxLayout()
        main_layout.setSpacing(20)

        # 左侧大图显示区域
        self.main_display_card = self.create_main_display()
        main_layout.addWidget(self.main_display_card, 2)  # 占2/3宽度

        # 右侧相机列表区域
        self.camera_list_widget = self.create_camera_list()
        main_layout.addWidget(self.camera_list_widget, 1)  # 占1/3宽度

        layout.addLayout(main_layout)

        # 控制面板
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)

        # 初始化相机视图
        self.init_camera_views()

        layout.addStretch(1)

    def create_main_display(self):
        """创建主显示区域"""
        card = CardWidget()
        card.setMinimumHeight(400)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 15, 15, 15)

        # 相机名称标题
        self.main_camera_title = BodyLabel("请选择相机")
        self.main_camera_title.setAlignment(Qt.AlignCenter)
        self.main_camera_title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(self.main_camera_title)

        # 主图像显示
        self.main_image_label = QLabel()
        self.main_image_label.setMinimumSize(600, 450)
        self.main_image_label.setAlignment(Qt.AlignCenter)
        self.main_image_label.setStyleSheet("background-color: #f0f0f0; border: 2px solid #ddd; border-radius: 8px;")
        self.main_image_label.setText("无图像")
        layout.addWidget(self.main_image_label)

        return card

    def create_camera_list(self):
        """创建相机列表区域"""
        from qfluentwidgets import ScrollArea

        # 创建滚动区域
        scroll_area = ScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)

        # 创建内容容器
        content_widget = QWidget()
        self.camera_list_layout = QVBoxLayout(content_widget)
        self.camera_list_layout.setSpacing(10)
        self.camera_list_layout.setContentsMargins(10, 10, 10, 10)

        scroll_area.setWidget(content_widget)
        return scroll_area

    def init_camera_views(self):
        """初始化相机显示视图"""
        # 清空现有的相机列表
        self.clear_camera_list()

        # 根据配置的相机数量创建显示区域
        if self.ctx and self.ctx.camera_manager:
            configs = self.ctx.camera_manager.get_camera_configs()
            enabled_configs = [config for config in configs if config.get("enabled", True)]

            for i, config in enumerate(enabled_configs):
                camera_widget = self.create_camera_thumbnail(config, i)
                self.camera_list_layout.addWidget(camera_widget)

            # 默认选择第一个相机
            if enabled_configs:
                self.select_camera(0, enabled_configs[0])
        else:
            # 如果没有配置管理器，创建默认的6个相机显示区域
            for i in range(6):
                default_config = {
                    "name": f"COD{i+1}",
                    "serial_number": "",
                    "id": f"CAMERA_{i+1:03d}"
                }
                camera_widget = self.create_camera_thumbnail(default_config, i)
                self.camera_list_layout.addWidget(camera_widget)

            # 默认选择第一个相机
            if True:  # 总是有默认配置
                default_config = {
                    "name": "COD1",
                    "serial_number": "",
                    "id": "CAMERA_001"
                }
                self.select_camera(0, default_config)

        # 添加弹性空间
        self.camera_list_layout.addStretch(1)

    def clear_camera_list(self):
        """清空相机列表"""
        while self.camera_list_layout.count() > 0:
            child = self.camera_list_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()

    def create_camera_thumbnail(self, config, camera_index):
        """创建相机缩略图控件"""
        card = CardWidget()
        card.setFixedHeight(120)
        card.setMinimumWidth(200)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(5)

        # 相机名称
        camera_name = config.get("name", f"相机 {camera_index + 1}")
        title = BodyLabel(camera_name)
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-weight: bold; font-size: 12px;")
        layout.addWidget(title)

        # 序列号（如果有）
        serial_number = config.get("serial_number", "")
        if serial_number:
            serial_label = BodyLabel(f"SN: {serial_number}")
            serial_label.setAlignment(Qt.AlignCenter)
            serial_label.setStyleSheet("color: #666; font-size: 9px;")
            layout.addWidget(serial_label)

        # 缩略图
        image_label = QLabel()
        image_label.setFixedSize(180, 60)
        image_label.setAlignment(Qt.AlignCenter)
        image_label.setStyleSheet("background-color: #f5f5f5; border: 1px solid #ddd; border-radius: 4px;")
        image_label.setText("无图像")
        layout.addWidget(image_label)

        # 存储信息
        card.image_label = image_label
        card.camera_index = camera_index
        card.camera_config = config
        card.camera_name = camera_name
        card.serial_number = serial_number
        card.is_selected = False

        # 点击事件
        card.mousePressEvent = lambda event, idx=camera_index, cfg=config: self.select_camera(idx, cfg)

        return card

    def select_camera(self, camera_index, config):
        """选择相机并在主显示区域显示"""
        # 更新主显示区域标题
        camera_name = config.get("name", f"相机 {camera_index + 1}")
        serial_number = config.get("serial_number", "")
        title_text = f"{camera_name}"
        if serial_number:
            title_text += f" (SN: {serial_number})"
        self.main_camera_title.setText(title_text)

        # 更新选中状态
        self.update_selection_state(camera_index)

        # 存储当前选中的相机
        self.current_selected_camera = camera_index
        self.current_selected_config = config

        # 触发相机抓拍并显示在主区域
        self.capture_and_display_main(camera_index)

    def update_selection_state(self, selected_index):
        """更新选中状态的视觉效果"""
        for i in range(self.camera_list_layout.count() - 1):  # -1 因为最后一个是stretch
            item = self.camera_list_layout.itemAt(i)
            if item and item.widget():
                card = item.widget()
                if hasattr(card, 'camera_index'):
                    if card.camera_index == selected_index:
                        # 选中状态
                        card.setStyleSheet("CardWidget { border: 2px solid #0078d4; background-color: #f0f8ff; }")
                        card.is_selected = True
                    else:
                        # 未选中状态
                        card.setStyleSheet("")
                        card.is_selected = False

    def create_camera_widget(self, config, camera_index):
        """创建单个相机显示控件"""
        card = CardWidget()
        card.setMinimumHeight(200)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(10, 10, 10, 10)

        # 使用配置中的名称
        camera_name = config.get("name", f"相机 {camera_index + 1}")
        title = BodyLabel(camera_name)
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # 显示序列号（如果有）
        serial_number = config.get("serial_number", "")
        if serial_number:
            serial_label = BodyLabel(f"SN: {serial_number}")
            serial_label.setAlignment(Qt.AlignCenter)
            serial_label.setStyleSheet("color: #666; font-size: 10px;")
            layout.addWidget(serial_label)

        image_label = QLabel()
        image_label.setMinimumSize(200, 150)
        image_label.setAlignment(Qt.AlignCenter)
        image_label.setStyleSheet("background-color: #f0f0f0; border: 1px solid #ccc;")
        image_label.setText("无图像")
        layout.addWidget(image_label)

        # 存储配置信息
        card.image_label = image_label
        card.camera_id = camera_index
        card.camera_config = config
        card.camera_name = camera_name
        card.serial_number = serial_number

        card.mousePressEvent = lambda event, cid=camera_index: self.on_camera_click(cid)

        return card

    def create_control_panel(self):
        """创建控制面板"""
        panel = CardWidget()
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)

        camera_label = BodyLabel("相机:")
        self.camera_combo = ComboBox()
        self.update_camera_combo()

        exposure_label = BodyLabel("曝光:")
        self.exposure_slider = Slider(Qt.Horizontal)
        self.exposure_slider.setRange(100, 100000)
        self.exposure_slider.setValue(10000)

        gain_label = BodyLabel("增益:")
        self.gain_slider = Slider(Qt.Horizontal)
        self.gain_slider.setRange(0, 20)
        self.gain_slider.setValue(10)

        save_button = PushButton("保存图像")
        save_button.clicked.connect(self.save_current_image)
        refresh_button = PushButton("抓拍刷新")
        refresh_button.clicked.connect(self.capture_and_display_all)

        layout.addWidget(camera_label)
        layout.addWidget(self.camera_combo)
        layout.addWidget(exposure_label)
        layout.addWidget(self.exposure_slider)
        layout.addWidget(gain_label)
        layout.addWidget(self.gain_slider)
        layout.addWidget(save_button)
        layout.addWidget(refresh_button)

        return panel

    def update_camera_combo(self):
        """更新相机下拉选择框"""
        self.camera_combo.clear()

        if self.ctx and self.ctx.camera_manager:
            configs = self.ctx.camera_manager.get_camera_configs()
            enabled_configs = [config for config in configs if config.get("enabled", True)]

            for i, config in enumerate(enabled_configs):
                camera_name = config.get("name", f"相机 {i + 1}")
                self.camera_combo.addItem(camera_name)
        else:
            # 默认添加6个相机
            for i in range(6):
                self.camera_combo.addItem(f"COD{i + 1}")

    def get_camera_widgets(self):
        """获取所有相机显示控件"""
        widgets = []
        for i in range(self.camera_grid.count()):
            item = self.camera_grid.itemAt(i)
            if item and item.widget():
                widgets.append(item.widget())
        return widgets

    def refresh_camera_display(self):
        """刷新相机显示"""
        # 重新初始化相机视图
        self.init_camera_views()

        # 更新下拉选择框
        self.update_camera_combo()

    def initialize_cameras_soft_trigger(self):
        """启动时将配置中指定的相机设置为软触发并开始取流"""
        try:
            cm = self.ctx.camera_manager
            ret = cm.enum_devices()
            if ret != 0:
                return

            # 只初始化配置中指定的相机
            configured_devices = cm.get_configured_devices()
            for device in configured_devices:
                try:
                    # 通过序列号打开设备
                    if cm.open_device(device.serial_number) != 0:
                        print(f"无法打开相机 {device.serial_number}")
                        continue

                    # 设置触发模式为软触发
                    cm.set_trigger_mode(device.serial_number, True)

                    # 应用配置中的参数
                    if hasattr(device, 'config_exposure_time'):
                        try:
                            cm.set_float_value(device.serial_number, "ExposureTime", device.config_exposure_time)
                        except Exception:
                            pass

                    if hasattr(device, 'config_gain'):
                        try:
                            cm.set_float_value(device.serial_number, "Gain", device.config_gain)
                        except Exception:
                            pass

                    # 建议关闭自动曝光和自动增益以稳定图像（若支持）
                    try:
                        cm.set_bool_value(device.serial_number, "ExposureAuto", False)
                        cm.set_bool_value(device.serial_number, "GainAuto", False)
                    except Exception:
                        pass

                    # 像素格式为 BGR8 以便直接渲染
                    try:
                        cm.set_enum_value(device.serial_number, "PixelFormat", PixelType_Gvsp_BGR8_Packed)
                    except Exception:
                        pass

                    # 开始取流
                    cm.start_grabbing(device.serial_number)
                    print(f"相机 {device.config_name} ({device.serial_number}) 初始化成功")

                except Exception as e:
                    print(f"初始化相机 {device.serial_number} 失败: {e}")

        except Exception as e:
            print(f"初始化软触发相机失败: {e}")

    def capture_and_display_main(self, camera_index):
        """对指定相机执行软触发并在主显示区域显示"""
        try:
            cm = self.ctx.camera_manager
            configured_devices = cm.get_configured_devices()

            if camera_index < len(configured_devices):
                device = configured_devices[camera_index]
                try:
                    # 触发后等待一帧
                    cm.trigger_software(device.serial_number)
                    frame = cm.get_frame_bgr(device.serial_number, timeout=1500) or cm.get_frame(device.serial_number, timeout=1500)
                    if frame:
                        img = self._frame_to_bgr_image(frame)
                        if img is not None:
                            self.update_main_display(img)
                            # 同时更新缩略图
                            self.update_thumbnail_display(camera_index, img)
                except Exception as e:
                    print(f"相机 {device.serial_number} 抓拍失败: {e}")
        except Exception as e:
            print(f"主显示抓拍失败: {e}")

    def capture_and_display_all(self):
        """对配置中指定的相机执行一次软触发并渲染缩略图"""
        try:
            cm = self.ctx.camera_manager
            configured_devices = cm.get_configured_devices()

            for idx, device in enumerate(configured_devices):
                try:
                    # 触发后等待一帧
                    cm.trigger_software(device.serial_number)
                    frame = cm.get_frame_bgr(device.serial_number, timeout=1500) or cm.get_frame(device.serial_number, timeout=1500)
                    if frame:
                        img = self._frame_to_bgr_image(frame)
                        if img is not None:
                            self.update_thumbnail_display(idx, img)
                            # 如果是当前选中的相机，也更新主显示
                            if hasattr(self, 'current_selected_camera') and self.current_selected_camera == idx:
                                self.update_main_display(img)
                except Exception as e:
                    print(f"相机 {device.serial_number} 抓拍失败: {e}")
                    continue
        except Exception as e:
            print(f"抓拍渲染失败: {e}")

    def _frame_to_bgr_image(self, frame):
        """将底层帧数据转换为 BGR ndarray"""
        width = frame.get("width", 0)
        height = frame.get("height", 0)
        frame_len = frame.get("frame_len", 0)
        data_bytes = frame.get("data")
        if not data_bytes or width <= 0 or height <= 0:
            return None
        expected_len = width * height * 3
        buf = data_bytes if frame_len >= expected_len else data_bytes[:expected_len]
        try:
            img = np.frombuffer(buf, dtype=np.uint8).reshape((height, width, 3))
            return img
        except Exception:
            return None

    def update_main_display(self, frame):
        """更新主显示区域的图像"""
        try:
            rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            h, w, ch = rgb_image.shape
            bytes_per_line = ch * w
            qt_image = QImage(
                rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888
            )

            pixmap = QPixmap.fromImage(qt_image)
            self.main_image_label.setPixmap(
                pixmap.scaled(
                    self.main_image_label.width(),
                    self.main_image_label.height(),
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation,
                )
            )
        except Exception as e:
            print(f"更新主显示时出错: {e}")

    def update_thumbnail_display(self, camera_index, frame):
        """更新指定相机的缩略图显示"""
        try:
            # 找到对应的缩略图控件
            for i in range(self.camera_list_layout.count() - 1):  # -1 因为最后一个是stretch
                item = self.camera_list_layout.itemAt(i)
                if item and item.widget():
                    card = item.widget()
                    if hasattr(card, 'camera_index') and card.camera_index == camera_index:
                        if hasattr(card, 'image_label'):
                            rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                            h, w, ch = rgb_image.shape
                            bytes_per_line = ch * w
                            qt_image = QImage(
                                rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888
                            )

                            pixmap = QPixmap.fromImage(qt_image)
                            card.image_label.setPixmap(
                                pixmap.scaled(
                                    card.image_label.width(),
                                    card.image_label.height(),
                                    Qt.KeepAspectRatio,
                                    Qt.SmoothTransformation,
                                )
                            )
                        break
        except Exception as e:
            print(f"更新缩略图显示时出错: {e}")

    def update_camera_display(self, camera_id, frame):
        """更新相机显示"""
        try:
            camera_widget = None
            for i in range(self.camera_grid.count()):
                widget = self.camera_grid.itemAt(i).widget()
                if (
                    widget
                    and hasattr(widget, "camera_id")
                    and widget.camera_id == camera_id
                ):
                    camera_widget = widget
                    break

            if camera_widget and hasattr(camera_widget, "image_label"):
                rgb_image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                h, w, ch = rgb_image.shape
                bytes_per_line = ch * w
                qt_image = QImage(
                    rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888
                )

                pixmap = QPixmap.fromImage(qt_image)
                camera_widget.image_label.setPixmap(
                    pixmap.scaled(
                        camera_widget.image_label.width(),
                        camera_widget.image_label.height(),
                        Qt.KeepAspectRatio,
                        Qt.SmoothTransformation,
                    )
                )
        except Exception as e:
            print(f"更新相机显示时出错: {e}")

    def on_camera_click(self, camera_id):
        """相机点击事件"""
        print(f"相机 {camera_id + 1} 被点击")

    def save_current_image(self):
        """保存当前图像"""
        print("保存图像功能待实现")
