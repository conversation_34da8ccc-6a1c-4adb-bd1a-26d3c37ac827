from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QGridLayout,
    QLabel,
    QTableWidget,
    QTableWidgetItem,
    QHeaderView,
    QFrame,
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QPixmap, QImage
from qfluentwidgets import (
    ScrollArea,
    BodyLabel,
    TitleLabel,
    CardWidget,
    PushButton,
    ComboBox,
    LineEdit,
)
import cv2
import numpy as np
from ui.base_page import BasePage


class DetectionPage(BasePage):
    """检测结果页面"""

    def __init__(self, ctx, parent=None):
        super().__init__(ctx, parent=parent)
        self.setObjectName("DetectionPage")
        self.detection_results = []  # 存储检测结果
        self.setup_ui()
        self.setup_timer()

    def setup_ui(self):
        """设置UI界面"""
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = TitleLabel("检测结果")
        layout.addWidget(title_label)

        # 搜索和筛选面板
        filter_panel = self.create_filter_panel()
        layout.addWidget(filter_panel)

        # 结果显示区域
        results_layout = QHBoxLayout()

        # 检测结果列表
        self.results_table = self.create_results_table()
        results_layout.addWidget(self.results_table, 2)

        # 图像显示区域
        self.image_display = self.create_image_display()
        results_layout.addWidget(self.image_display, 1)

        layout.addLayout(results_layout)

        # 操作按钮
        button_layout = QHBoxLayout()
        export_button = PushButton("导出结果")
        export_button.clicked.connect(self.export_results)
        button_layout.addWidget(export_button)
        button_layout.addStretch(1)
        layout.addLayout(button_layout)

        # 设置布局
        layout.addStretch(1)

    def create_filter_panel(self):
        """创建筛选面板"""
        panel = CardWidget()
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)

        # 搜索框
        search_label = BodyLabel("搜索:")
        self.search_edit = LineEdit()
        self.search_edit.setPlaceholderText("输入搜索关键词")

        # 筛选器
        filter_label = BodyLabel("筛选:")
        self.filter_combo = ComboBox()
        self.filter_combo.addItem("全部")
        self.filter_combo.addItem("有缺陷")
        self.filter_combo.addItem("无缺陷")

        # 查询按钮
        query_button = PushButton("查询")
        query_button.clicked.connect(self.query_results)

        # 添加控件到布局
        layout.addWidget(search_label)
        layout.addWidget(self.search_edit)
        layout.addWidget(filter_label)
        layout.addWidget(self.filter_combo)
        layout.addWidget(query_button)

        return panel

    def create_results_table(self):
        """创建结果表格"""
        table = QTableWidget()
        table.setColumnCount(6)
        table.setHorizontalHeaderLabels(
            ["任务ID", "相机ID", "检测时间", "是否缺陷", "缺陷数量", "置信度"]
        )

        # 设置表格属性
        table.setSelectionBehavior(QTableWidget.SelectRows)
        table.setSelectionMode(QTableWidget.SingleSelection)
        table.setEditTriggers(QTableWidget.NoEditTriggers)

        # 设置列宽
        header = table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(5, QHeaderView.Stretch)

        # 连接选择事件
        table.cellClicked.connect(self.on_result_selected)

        return table

    def create_image_display(self):
        """创建图像显示区域"""
        card = CardWidget()
        layout = QVBoxLayout(card)
        layout.setContentsMargins(10, 10, 10, 10)

        # 标题
        title = BodyLabel("检测图像")
        title.setAlignment(Qt.AlignCenter)
        layout.addWidget(title)

        # 图像显示
        self.image_label = QLabel()
        self.image_label.setMinimumSize(300, 200)
        self.image_label.setAlignment(Qt.AlignCenter)
        self.image_label.setStyleSheet(
            "background-color: #f0f0f0; border: 1px solid #ccc;"
        )
        self.image_label.setText("选择结果查看图像")
        layout.addWidget(self.image_label)

        # 缺陷信息
        self.defect_info = BodyLabel("缺陷信息: 无")
        layout.addWidget(self.defect_info)

        return card

    def setup_timer(self):
        """设置定时器，定期更新检测结果"""
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_detection_results)
        self.timer.start(2000)  # 每2秒更新一次

    def update_detection_results(self):
        """更新检测结果"""
        try:
            # 模拟获取检测结果
            # 实际应该从detector_manager或data_manager获取
            new_results = self.generate_dummy_results()

            # 更新结果列表
            self.detection_results = new_results
            self.refresh_results_table()
        except Exception as e:
            print(f"更新检测结果时出错: {e}")

    def generate_dummy_results(self):
        """生成模拟的检测结果（用于测试）"""
        results = []
        for i in range(10):
            result = {
                "task_id": f"task_{i+1:03d}",
                "camera_id": f"camera_{(i % 6) + 1}",
                "timestamp": f"2023-06-{15+(i%15):02d} 10:{(i*5) % 60:02d}:{(i*3) % 60:02d}",
                "is_defect": i % 3 == 0,  # 每3个中有1个有缺陷
                "defect_count": i % 3,
                "confidence": 0.85 + (i % 15) * 0.01,
            }
            results.append(result)
        return results

    def refresh_results_table(self):
        """刷新结果表格"""
        # 清空表格
        self.results_table.setRowCount(0)

        # 添加数据
        for result in self.detection_results:
            row = self.results_table.rowCount()
            self.results_table.insertRow(row)

            # 添加数据项
            self.results_table.setItem(row, 0, QTableWidgetItem(result["task_id"]))
            self.results_table.setItem(row, 1, QTableWidgetItem(result["camera_id"]))
            self.results_table.setItem(row, 2, QTableWidgetItem(result["timestamp"]))

            # 是否缺陷
            defect_item = QTableWidgetItem("是" if result["is_defect"] else "否")
            defect_item.setTextAlignment(Qt.AlignCenter)
            if result["is_defect"]:
                defect_item.setForeground(Qt.red)
            self.results_table.setItem(row, 3, defect_item)

            # 缺陷数量
            count_item = QTableWidgetItem(str(result["defect_count"]))
            count_item.setTextAlignment(Qt.AlignCenter)
            self.results_table.setItem(row, 4, count_item)

            # 置信度
            conf_item = QTableWidgetItem(f"{result['confidence']:.2f}")
            conf_item.setTextAlignment(Qt.AlignCenter)
            self.results_table.setItem(row, 5, conf_item)

    def on_result_selected(self, row, column):
        """结果选择事件"""
        try:
            if row >= 0 and row < len(self.detection_results):
                result = self.detection_results[row]
                # 显示图像（模拟）
                self.display_result_image(result)
                # 显示缺陷信息
                self.display_defect_info(result)
        except Exception as e:
            print(f"选择结果时出错: {e}")

    def display_result_image(self, result):
        """显示结果图像"""
        try:
            # 生成模拟图像
            image = self.generate_result_image(result)

            # 转换为QImage并显示
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            h, w, ch = rgb_image.shape
            bytes_per_line = ch * w
            qt_image = QImage(
                rgb_image.data, w, h, bytes_per_line, QImage.Format_RGB888
            )

            # 显示图像
            pixmap = QPixmap.fromImage(qt_image)
            self.image_label.setPixmap(
                pixmap.scaled(
                    self.image_label.width(),
                    self.image_label.height(),
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation,
                )
            )
        except Exception as e:
            print(f"显示结果图像时出错: {e}")

    def generate_result_image(self, result):
        """生成结果图像（用于测试）"""

        # 创建一个彩色图像
        height, width = 300, 300
        image = np.zeros((height, width, 3), dtype=np.uint8)

        # 设置背景色
        if result["is_defect"]:
            image[:] = (0, 0, 255)  # 红色表示有缺陷
        else:
            image[:] = (0, 255, 0)  # 绿色表示无缺陷

        # 添加文本信息
        if cv2 is not None:
            cv2.putText(
                image,
                f"Task: {result['task_id']}",
                (10, 30),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.6,
                (255, 255, 255),
                1,
            )
            cv2.putText(
                image,
                f"Camera: {result['camera_id']}",
                (10, 60),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.6,
                (255, 255, 255),
                1,
            )
            cv2.putText(
                image,
                f"Defect: {'Yes' if result['is_defect'] else 'No'}",
                (10, 90),
                cv2.FONT_HERSHEY_SIMPLEX,
                0.6,
                (255, 255, 255),
                1,
            )

        # 如果有缺陷，添加一些模拟的缺陷框
        if result["is_defect"] and cv2 is not None:
            for i in range(result["defect_count"]):
                x = 50 + i * 60
                y = 150 + i * 30
                cv2.rectangle(image, (x, y), (x + 40, y + 40), (255, 255, 0), 2)
                cv2.putText(
                    image,
                    f"D{i+1}",
                    (x, y - 5),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    0.5,
                    (255, 255, 0),
                    1,
                )

        return image

    def display_defect_info(self, result):
        """显示缺陷信息"""
        if result["is_defect"]:
            info = f"缺陷信息: 发现 {result['defect_count']} 个缺陷，置信度 {result['confidence']:.2f}"
        else:
            info = "缺陷信息: 无缺陷"
        self.defect_info.setText(info)

    def query_results(self):
        """查询结果"""
        print("查询结果功能待实现")
        # 这里应该实现根据搜索条件和筛选器查询结果的功能

    def export_results(self):
        """导出结果"""
        print("导出结果功能待实现")
        # 这里应该实现导出结果到文件的功能
