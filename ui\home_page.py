from PySide6.QtWidgets import (
    QWidget,
    QVBoxLayout,
    QHBoxLayout,
    QGridLayout,
    QLabel,
    QFrame,
)
from PySide6.QtCore import Qt, QTimer
from qfluentwidgets import (
    ScrollArea,
    BodyLabel,
    TitleLabel,
    CardWidget,
    ProgressBar,
    PushButton,
)
from PySide6.QtGui import QFont

from camera.camera_manager import CameraManager
from plc.plc_handler import PlcHandler
from ui.base_page import BasePage


class HomePage(BasePage):
    """首页（系统总览）页面"""

    def __init__(self, ctx, parent=None):
        super().__init__(ctx, parent=parent)
        self.setObjectName("HomePage")
        self.setup_ui()
        self.setup_timer()

    def setup_ui(self):
        """设置UI界面"""
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = TitleLabel("系统总览")
        layout.addWidget(title_label)

        # 网格布局用于放置状态卡片
        grid_layout = QGridLayout()
        grid_layout.setSpacing(15)
        layout.addLayout(grid_layout)

        # 系统状态卡片
        self.system_status_card = self.create_status_card(
            "系统状态", "运行中", "#4CAF50"
        )
        grid_layout.addWidget(self.system_status_card, 0, 0)

        # 相机状态卡片
        self.camera_status_card = self.create_status_card(
            "相机连接", "未连接", "#F44336"
        )
        grid_layout.addWidget(self.camera_status_card, 0, 1)

        # PLC状态卡片
        self.plc_status_card = self.create_status_card("PLC连接", "未连接", "#F44336")
        grid_layout.addWidget(self.plc_status_card, 0, 2)

        # 检测统计卡片
        self.detection_stats_card = self.create_stats_card("检测统计")
        grid_layout.addWidget(self.detection_stats_card, 1, 0, 1, 3)

        # PLC 测试控制卡片
        self.plc_control_card = self.create_plc_control_card("PLC 测试控制")
        layout.addWidget(self.plc_control_card)

        # 设置布局
        layout.addStretch(1)

    def create_status_card(self, title, status, color):
        """创建状态卡片"""
        card = CardWidget()
        card.setMinimumHeight(120)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 15, 15, 15)

        # 标题
        title_label = BodyLabel(title)
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        layout.addWidget(title_label)

        # 状态
        status_label = QLabel(status)
        status_label.setStyleSheet(
            f"color: {color}; font-size: 16px; font-weight: bold;"
        )
        layout.addWidget(status_label)

        layout.addStretch(1)
        return card

    def create_stats_card(self, title):
        """创建统计卡片"""
        card = CardWidget()
        card.setMinimumHeight(150)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 15, 15, 15)

        # 标题
        title_label = BodyLabel(title)
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        layout.addWidget(title_label)

        # 统计信息
        stats_layout = QHBoxLayout()
        stats_layout.setSpacing(20)

        # 检测数量
        count_layout = QVBoxLayout()
        count_label = BodyLabel("检测数量")
        self.count_value = QLabel("0")
        self.count_value.setStyleSheet(
            "font-size: 20px; font-weight: bold; color: #2196F3;"
        )
        count_layout.addWidget(count_label)
        count_layout.addWidget(self.count_value)
        stats_layout.addLayout(count_layout)

        # 缺陷率
        defect_layout = QVBoxLayout()
        defect_label = BodyLabel("缺陷率")
        self.defect_value = QLabel("0.00%")
        self.defect_value.setStyleSheet(
            "font-size: 20px; font-weight: bold; color: #FF5722;"
        )
        defect_layout.addWidget(defect_label)
        defect_layout.addWidget(self.defect_value)
        stats_layout.addLayout(defect_layout)

        # 进度条
        progress_layout = QVBoxLayout()
        progress_label = BodyLabel("处理进度")
        self.progress_bar = ProgressBar()
        self.progress_bar.setValue(0)
        progress_layout.addWidget(progress_label)
        progress_layout.addWidget(self.progress_bar)
        stats_layout.addLayout(progress_layout)

        layout.addLayout(stats_layout)
        layout.addStretch(1)

        return card

    def create_plc_control_card(self, title: str) -> CardWidget:
        """创建 PLC 测试控制卡片（连接/断开、振动盘开/关）"""
        card = CardWidget()
        card.setMinimumHeight(100)

        layout = QVBoxLayout(card)
        layout.setContentsMargins(15, 15, 15, 15)

        title_label = BodyLabel(title)
        title_label.setFont(QFont("Microsoft YaHei", 12, QFont.Bold))
        layout.addWidget(title_label)

        row = QHBoxLayout()
        btn_connect = PushButton("PLC连接")
        btn_disconnect = PushButton("PLC断开")
        btn_vib_on = PushButton("振动盘开")
        btn_vib_off = PushButton("振动盘关")

        btn_connect.clicked.connect(self.on_plc_connect)
        btn_disconnect.clicked.connect(self.on_plc_disconnect)
        btn_vib_on.clicked.connect(self.on_vibrator_on)
        btn_vib_off.clicked.connect(self.on_vibrator_off)

        row.addWidget(btn_connect)
        row.addWidget(btn_disconnect)
        row.addWidget(btn_vib_on)
        row.addWidget(btn_vib_off)
        layout.addLayout(row)

        # 第二行：运行/停止/缓停/状态、旋转相关（无参数）
        row2 = QHBoxLayout()
        btn_run = PushButton("运行")
        btn_stop = PushButton("停止")
        btn_stop_delay = PushButton("缓停")
        btn_status = PushButton("状态")
        btn_rot_right_on = PushButton("右转开")
        btn_rot_right_off = PushButton("右转关")
        btn_rot_left_on = PushButton("左转开")
        btn_rot_left_off = PushButton("左转关")

        btn_run.clicked.connect(self.on_plc_run)
        btn_stop.clicked.connect(self.on_plc_stop)
        btn_stop_delay.clicked.connect(self.on_plc_stop_delay)
        btn_status.clicked.connect(self.on_plc_status)
        btn_rot_right_on.clicked.connect(lambda: self.on_rotate(True))
        btn_rot_right_off.clicked.connect(lambda: self.on_rotate(False))
        btn_rot_left_on.clicked.connect(lambda: self.on_inv_rotate(True))
        btn_rot_left_off.clicked.connect(lambda: self.on_inv_rotate(False))

        for b in [
            btn_run,
            btn_stop,
            btn_stop_delay,
            btn_status,
            btn_rot_right_on,
            btn_rot_right_off,
            btn_rot_left_on,
            btn_rot_left_off,
        ]:
            row2.addWidget(b)

        layout.addLayout(row2)

        # 第三行：吹气测试（无参数开始/结束）
        row3 = QHBoxLayout()
        btn_blow_on = PushButton("吹气测试开始")
        btn_blow_off = PushButton("吹气测试结束")
        btn_blow_on.clicked.connect(lambda: self.on_blow_test(True))
        btn_blow_off.clicked.connect(lambda: self.on_blow_test(False))
        row3.addWidget(btn_blow_on)
        row3.addWidget(btn_blow_off)
        layout.addLayout(row3)

        return card

    def setup_timer(self):
        """设置定时器，定期更新状态"""
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_status)
        self.timer.start(1000)  # 每秒更新一次

    def update_status(self):
        """更新系统状态"""
        try:
            # 更新系统状态（这里简化处理，实际应根据系统运行状态判断）
            self.update_card_status(self.system_status_card, "运行中", "#4CAF50")

            # 更新相机状态
            if (
                hasattr(self, "ctx")
                and self.ctx
                and getattr(self.ctx, "camera_manager", None)
            ):
                camera_count = len(self.ctx.camera_manager.devices)
                status_text = f"已连接 {camera_count} 台相机"
                color = "#4CAF50" if camera_count > 0 else "#F44336"
                self.update_card_status(self.camera_status_card, status_text, color)

            # 更新PLC状态
            if (
                hasattr(self, "ctx")
                and self.ctx
                and getattr(self.ctx, "plc_handler", None)
            ):
                # 尝试通过一次状态查询判定连接性
                try:
                    self.ctx.plc_handler.get_run_status()
                    plc_ok = True
                except Exception:
                    plc_ok = False
                status_text = "已连接" if plc_ok else "未连接"
                color = "#4CAF50" if plc_ok else "#F44336"
                self.update_card_status(self.plc_status_card, status_text, color)

        except Exception as e:
            print(f"更新状态时出错: {e}")

    def update_card_status(self, card, status, color):
        """更新卡片状态"""
        # 找到卡片中的状态标签并更新
        for i in range(card.layout().count()):
            item = card.layout().itemAt(i)
            if item.widget() and isinstance(item.widget(), QLabel):
                label = item.widget()
                if (
                    label.text() != "系统状态"
                    and label.text() != "相机连接"
                    and label.text() != "PLC连接"
                    and label.text() != "检测统计"
                ):
                    label.setText(status)
                    label.setStyleSheet(
                        f"color: {color}; font-size: 16px; font-weight: bold;"
                    )
                    break

    def update_detection_stats(self, total_count, defect_count):
        """更新检测统计信息"""
        self.count_value.setText(str(total_count))
        if total_count > 0:
            defect_rate = (defect_count / total_count) * 100
            self.defect_value.setText(f"{defect_rate:.2f}%")
        else:
            self.defect_value.setText("0.00%")

    # PLC 测试按钮槽函数
    def on_plc_connect(self):
        try:
            if self.ctx and getattr(self.ctx, "plc_handler", None):
                self.ctx.plc_handler.connect()
        except Exception as e:
            print(f"PLC 连接失败: {e}")

    def on_plc_disconnect(self):
        try:
            if self.ctx and getattr(self.ctx, "plc_handler", None):
                self.ctx.plc_handler.disconnect()
        except Exception as e:
            print(f"PLC 断开失败: {e}")

    def on_vibrator_on(self):
        try:
            if self.ctx and getattr(self.ctx, "plc_handler", None):
                self.ctx.plc_handler.open_vibrator(True)
                # res = self.ctx.plc_handler.get_blow_pos(0)
                # print(res)
        except Exception as e:
            print(f"振动盘打开失败: {e}")

    def on_vibrator_off(self):
        try:
            if self.ctx and getattr(self.ctx, "plc_handler", None):
                self.ctx.plc_handler.open_vibrator(False)
        except Exception as e:
            print(f"振动盘关闭失败: {e}")

    def on_plc_run(self):
        try:
            if self.ctx and getattr(self.ctx, "plc_handler", None):
                self.ctx.plc_handler.run()
        except Exception as e:
            print(f"PLC 运行失败: {e}")

    def on_plc_stop(self):
        try:
            if self.ctx and getattr(self.ctx, "plc_handler", None):
                self.ctx.plc_handler.stop()
        except Exception as e:
            print(f"PLC 停止失败: {e}")

    def on_plc_stop_delay(self):
        try:
            if self.ctx and getattr(self.ctx, "plc_handler", None):
                self.ctx.plc_handler.stop_delay()
        except Exception as e:
            print(f"PLC 缓停失败: {e}")

    def on_plc_status(self):
        try:
            if self.ctx and getattr(self.ctx, "plc_handler", None):
                self.ctx.plc_handler.get_run_status()
        except Exception as e:
            print(f"查询状态失败: {e}")

    def on_rotate(self, on: bool):
        try:
            if self.ctx and getattr(self.ctx, "plc_handler", None):
                self.ctx.plc_handler.rotate(on)
        except Exception as e:
            print(f"右转操作失败: {e}")

    def on_inv_rotate(self, on: bool):
        try:
            if self.ctx and getattr(self.ctx, "plc_handler", None):
                self.ctx.plc_handler.inv_rotate(on)
        except Exception as e:
            print(f"左转操作失败: {e}")

    def on_blow_test(self, on: bool):
        try:
            if self.ctx and getattr(self.ctx, "plc_handler", None):
                self.ctx.plc_handler.blow_test(on)
        except Exception as e:
            print(f"吹气测试操作失败: {e}")
