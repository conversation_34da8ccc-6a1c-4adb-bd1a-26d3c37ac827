from PySide6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QTextEdit, QFrame
from PySide6.QtCore import Qt, QTimer
from qfluentwidgets import (
    ScrollArea,
    BodyLabel,
    TitleLabel,
    CardWidget,
    PushButton,
    ComboBox,
)
from ui.base_page import BasePage


class LogPage(BasePage):
    """日志页面"""

    def __init__(self, ctx, parent=None):
        super().__init__(ctx, parent=parent)
        self.setObjectName("LogPage")
        self.setup_ui()
        self.setup_timer()

    def setup_ui(self):
        """设置UI界面"""
        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 标题
        title_label = TitleLabel("日志")
        layout.addWidget(title_label)

        # 控制面板
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)

        # 日志显示区域
        log_card = CardWidget()
        log_layout = QVBoxLayout(log_card)
        log_layout.setContentsMargins(10, 10, 10, 10)

        # 日志文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet(
            """
            QTextEdit {
                background-color: #000000;
                color: #ffffff;
                font-family: Consolas, Monaco, 'Courier New', monospace;
                font-size: 12px;
            }
        """
        )
        log_layout.addWidget(self.log_text)

        layout.addWidget(log_card)

        # 操作按钮
        button_layout = QHBoxLayout()
        clear_button = PushButton("清空日志")
        clear_button.clicked.connect(self.clear_logs)
        export_button = PushButton("导出日志")
        export_button.clicked.connect(self.export_logs)
        button_layout.addWidget(clear_button)
        button_layout.addWidget(export_button)
        button_layout.addStretch(1)
        layout.addLayout(button_layout)

    def create_control_panel(self):
        """创建控制面板"""
        panel = CardWidget()
        layout = QHBoxLayout(panel)
        layout.setContentsMargins(15, 15, 15, 15)

        # 日志级别筛选
        level_label = BodyLabel("日志级别:")
        self.level_combo = ComboBox()
        self.level_combo.addItem("全部")
        self.level_combo.addItem("DEBUG")
        self.level_combo.addItem("INFO")
        self.level_combo.addItem("WARNING")
        self.level_combo.addItem("ERROR")
        self.level_combo.setCurrentText("INFO")

        # 刷新按钮
        refresh_button = PushButton("刷新")
        refresh_button.clicked.connect(self.refresh_logs)

        # 添加控件到布局
        layout.addWidget(level_label)
        layout.addWidget(self.level_combo)
        layout.addWidget(refresh_button)
        layout.addStretch(1)

        return panel

    def setup_timer(self):
        """设置定时器，定期更新日志"""
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_logs)
        self.timer.start(1000)  # 每秒更新一次

    def update_logs(self):
        """更新日志显示"""
        try:
            # 获取最近的日志
            recent_logs = self.get_recent_logs()

            # 更新日志显示
            self.log_text.clear()
            for log in recent_logs:
                self.log_text.append(log)

            # 滚动到底部
            self.log_text.verticalScrollBar().setValue(
                self.log_text.verticalScrollBar().maximum()
            )
        except Exception as e:
            print(f"更新日志时出错: {e}")

    def get_recent_logs(self):
        """获取最近的日志（模拟实现）"""
        # 实际应该从logger获取日志
        logs = []
        import datetime

        current_time = datetime.datetime.now()

        for i in range(20):
            time_str = (current_time - datetime.timedelta(seconds=i * 2)).strftime(
                "%Y-%m-%d %H:%M:%S"
            )
            levels = ["DEBUG", "INFO", "WARNING", "ERROR"]
            level = levels[i % len(levels)]

            messages = [
                "系统初始化完成",
                "相机连接成功",
                "开始图像采集",
                "图像采集完成",
                "提交检测任务",
                "检测任务完成",
                "保存检测结果",
                "PLC通信正常",
            ]
            message = messages[i % len(messages)]

            log_entry = f"[{time_str}] [{level:>7}] {message}"
            logs.append(log_entry)

        return reversed(logs)  # 返回最新的在前面

    def refresh_logs(self):
        """刷新日志"""
        self.update_logs()

    def clear_logs(self):
        """清空日志"""
        self.log_text.clear()
        print("日志已清空")

    def export_logs(self):
        """导出日志"""
        print("导出日志功能待实现")
        # 这里应该实现导出日志到文件的功能
        if self.parent and hasattr(self.parent, "show_success_info"):
            self.parent.show_success_info("日志", "日志已导出")
