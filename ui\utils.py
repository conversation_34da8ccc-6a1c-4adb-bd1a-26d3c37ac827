"""
工具函数
"""

from PySide6.QtGui import QPixmap, QImage, Qt
import cv2


def convert_cv_to_qpixmap(cv_img):
    """
    将OpenCV图像转换为QPixmap

    Args:
        cv_img: OpenCV图像 (numpy数组)

    Returns:
        QPixmap: 转换后的QPixmap对象
    """

    try:
        # 如果是灰度图像
        if len(cv_img.shape) == 2:
            height, width = cv_img.shape
            bytes_per_line = width
            qt_image = QImage(
                cv_img.data, width, height, bytes_per_line, QImage.Format_Grayscale8
            )
        # 如果是彩色图像
        elif len(cv_img.shape) == 3:
            height, width, channels = cv_img.shape
            bytes_per_line = channels * width

            # BGR转RGB
            if channels == 3:
                rgb_image = cv2.cvtColor(cv_img, cv2.COLOR_BGR2RGB)
                qt_image = QImage(
                    rgb_image.data, width, height, bytes_per_line, QImage.Format_RGB888
                )
            # BGRA转RGBA
            elif channels == 4:
                rgba_image = cv2.cvtColor(cv_img, cv2.COLOR_BGRA2RGBA)
                qt_image = QImage(
                    rgba_image.data,
                    width,
                    height,
                    bytes_per_line,
                    QImage.Format_RGBA8888,
                )
        else:
            return QPixmap()

        return QPixmap.fromImage(qt_image)
    except Exception as e:
        print(f"图像转换错误: {e}")
        return QPixmap()


def resize_pixmap_keep_aspect(pixmap, max_width, max_height):
    """
    保持宽高比调整QPixmap大小

    Args:
        pixmap: 原始QPixmap
        max_width: 最大宽度
        max_height: 最大高度

    Returns:
        QPixmap: 调整大小后的QPixmap
    """
    if pixmap.isNull():
        return pixmap

    # 计算缩放比例
    ratio = min(max_width / pixmap.width(), max_height / pixmap.height())

    # 如果图像小于最大尺寸，不放大
    if ratio >= 1:
        return pixmap

    new_width = int(pixmap.width() * ratio)
    new_height = int(pixmap.height() * ratio)

    return pixmap.scaled(
        new_width,
        new_height,
        aspectMode=Qt.AspectRatioMode.KeepAspectRatio,
        mode=Qt.TransformationMode.SmoothTransformation,
    )


def format_time(seconds):
    """
    格式化时间显示

    Args:
        seconds: 秒数

    Returns:
        str: 格式化后的时间字符串
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = int(seconds // 60)
        secs = seconds % 60
        return f"{minutes}m {secs:.1f}s"
    else:
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        return f"{hours}h {minutes}m {secs:.1f}s"


def format_bytes(bytes_size):
    """
    格式化字节大小显示

    Args:
        bytes_size: 字节大小

    Returns:
        str: 格式化后的大小字符串
    """
    for unit in ["B", "KB", "MB", "GB", "TB"]:
        if bytes_size < 1024.0:
            return f"{bytes_size:.1f} {unit}"
        bytes_size /= 1024.0
    return f"{bytes_size:.1f} PB"


def validate_ip_address(ip):
    """
    验证IP地址格式

    Args:
        ip: IP地址字符串

    Returns:
        bool: 是否为有效的IP地址
    """
    import re

    pattern = r"^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$"
    match = re.match(pattern, ip)

    if not match:
        return False

    # 检查每个数字是否在0-255范围内
    for i in range(1, 5):
        num = int(match.group(i))
        if num < 0 or num > 255:
            return False

    return True


def validate_port(port):
    """
    验证端口号

    Args:
        port: 端口号

    Returns:
        bool: 是否为有效的端口号
    """
    try:
        port_num = int(port)
        return 1 <= port_num <= 65535
    except (ValueError, TypeError):
        return False
